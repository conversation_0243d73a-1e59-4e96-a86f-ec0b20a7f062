"use server"

import { query } from "@/lib/db"

export async function getAnalyticsData() {
  try {
    const [userGrowthResult, contentGrowthResult, topSubjectsResult, recentActivityResult] = await Promise.all([
      // User growth over last 6 months
      query(`
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as count
        FROM users 
        WHERE created_at >= NOW() - INTERVAL '6 months'
        GROUP BY DATE_TRUNC('month', created_at)
        ORDER BY month
      `),
      // Content growth over last 6 months
      query(`
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as books,
          (SELECT COUNT(*) FROM chapters WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', books.created_at)) as chapters
        FROM books 
        WHERE created_at >= NOW() - INTERVAL '6 months'
        GROUP BY DATE_TRUNC('month', created_at)
        ORDER BY month
      `),
      // Top subjects by content count
      query(`
        SELECT 
          s.name,
          COUNT(*) as content_count
        FROM subjects s
        LEFT JOIN book_subjects bs ON s.id = bs.subject_id
        LEFT JOIN chapter_subjects cs ON s.id = cs.subject_id
        LEFT JOIN video_subjects vs ON s.id = vs.subject_id
        GROUP BY s.id, s.name
        ORDER BY content_count DESC
        LIMIT 10
      `),
      // Recent activity
      query(`
        SELECT 
          al.action_type,
          al.entity_type,
          al.details,
          al.created_at,
          u.full_name as user_name
        FROM activity_logs al
        JOIN users u ON al.user_id = u.id
        ORDER BY al.created_at DESC
        LIMIT 20
      `),
    ])

    return {
      userGrowth: userGrowthResult.rows,
      contentGrowth: contentGrowthResult.rows,
      topSubjects: topSubjectsResult.rows,
      recentActivity: recentActivityResult.rows,
    }
  } catch (error) {
    console.error("Error fetching analytics data:", error)
    throw new Error("Failed to fetch analytics data")
  }
}

export async function getUsageStats() {
  try {
    const [dailyStats, weeklyStats, monthlyStats] = await Promise.all([
      query(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as activities
        FROM activity_logs 
        WHERE created_at >= NOW() - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY date
      `),
      query(`
        SELECT 
          DATE_TRUNC('week', created_at) as week,
          COUNT(*) as activities
        FROM activity_logs 
        WHERE created_at >= NOW() - INTERVAL '12 weeks'
        GROUP BY DATE_TRUNC('week', created_at)
        ORDER BY week
      `),
      query(`
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as activities
        FROM activity_logs 
        WHERE created_at >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', created_at)
        ORDER BY month
      `),
    ])

    return {
      daily: dailyStats.rows,
      weekly: weeklyStats.rows,
      monthly: monthlyStats.rows,
    }
  } catch (error) {
    console.error("Error fetching usage stats:", error)
    throw new Error("Failed to fetch usage stats")
  }
}
