"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { auth } from "@/lib/auth"
import { z } from "zod"
import { query } from "@/lib/db"

const formSchema = z.object({
  title: z.string().min(3, {
    message: "Title must be at least 3 characters.",
  }),
  slug: z.string().min(3, {
    message: "Slug must be at least 3 characters.",
  }),
  content: z.string().min(10, {
    message: "Content must be at least 10 characters.",
  }),
  status: z.enum(["draft", "published"]),
})

export type State = {
  errors?: {
    title?: string[]
    slug?: string[]
    content?: string[]
  }
  message?: string | null
}

export async function createContent(prevState: State, formData: FormData): Promise<State> {
  const session = await auth()
  if (!session?.user) {
    return {
      message: "Unauthorized",
    }
  }

  const validatedFields = formSchema.safeParse({
    title: formData.get("title"),
    slug: formData.get("slug"),
    content: formData.get("content"),
    status: formData.get("status"),
  })

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Missing Fields. Failed to Create Content.",
    }
  }

  const { title, slug, content, status } = validatedFields.data

  try {
    await query("INSERT INTO content (title, slug, content, status, author_id) VALUES ($1, $2, $3, $4, $5)", [
      title,
      slug,
      content,
      status,
      session.user.id,
    ])
  } catch (e: any) {
    console.error(e)
    return {
      message: "Failed to create content",
    }
  }

  revalidatePath("/admin/content")
  redirect("/admin/content")
}

export async function updateContent(id: string, prevState: State, formData: FormData): Promise<State> {
  const session = await auth()
  if (!session?.user) {
    return {
      message: "Unauthorized",
    }
  }

  const validatedFields = formSchema.safeParse({
    title: formData.get("title"),
    slug: formData.get("slug"),
    content: formData.get("content"),
    status: formData.get("status"),
  })

  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Missing Fields. Failed to Update Content.",
    }
  }

  const { title, slug, content, status } = validatedFields.data

  try {
    await query(
      "UPDATE content SET title = $1, slug = $2, content = $3, status = $4 WHERE id = $5 AND author_id = $6",
      [title, slug, content, status, id, session.user.id],
    )
  } catch (e: any) {
    console.error(e)
    return {
      message: "Failed to update content",
    }
  }

  revalidatePath("/admin/content")
  redirect("/admin/content")
}

export async function deleteContent(id: string) {
  const session = await auth()
  if (!session?.user) {
    return {
      message: "Unauthorized",
    }
  }

  try {
    await query("DELETE FROM content WHERE id = $1 AND author_id = $2", [id, session.user.id])
    revalidatePath("/admin/content")
    return { message: "Deleted Content" }
  } catch (e: any) {
    console.error(e)
    return { message: "Failed to delete content" }
  }
}

export async function getContent(id: string) {
  "use server"
  console.log(`Getting content with id: ${id}`)
  // Placeholder for content retrieval logic
  return { id, title: "Example Content", body: "This is example content." }
}
