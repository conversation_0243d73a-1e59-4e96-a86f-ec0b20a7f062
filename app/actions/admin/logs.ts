"use server"

import { query } from "@/lib/db"

export type ActivityLog = {
  id: number
  actionType: string
  entityType: string
  entityId: number
  details: string
  createdAt: string
  userName: string
}

export async function getActivityLogs(
  searchTerm = "",
  actionType = "all",
  entityType = "all",
  limit = 50,
): Promise<ActivityLog[]> {
  try {
    let sql = `
      SELECT 
        al.id,
        al.action_type as "actionType",
        al.entity_type as "entityType",
        al.entity_id as "entityId",
        al.details,
        al.created_at as "createdAt",
        u.full_name as "userName"
      FROM activity_logs al
      JOIN users u ON al.user_id = u.id
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    if (searchTerm) {
      sql += ` AND (al.details ILIKE $${paramIndex} OR u.full_name ILIKE $${paramIndex})`
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    if (actionType !== "all") {
      sql += ` AND al.action_type = $${paramIndex}`
      params.push(actionType)
      paramIndex++
    }

    if (entityType !== "all") {
      sql += ` AND al.entity_type = $${paramIndex}`
      params.push(entityType)
      paramIndex++
    }

    sql += ` ORDER BY al.created_at DESC LIMIT $${paramIndex}`
    params.push(limit)

    const result = await query(sql, params)

    return result.rows.map((row) => ({
      ...row,
      createdAt: new Date(row.createdAt).toISOString(),
    }))
  } catch (error) {
    console.error("Error fetching activity logs:", error)
    throw new Error("Failed to fetch activity logs")
  }
}

export async function createActivityLog(logData: {
  userId: number
  actionType: string
  entityType: string
  entityId: number
  details: string
}) {
  try {
    const sql = `
      INSERT INTO activity_logs (user_id, action_type, entity_type, entity_id, details)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id
    `

    const result = await query(sql, [
      logData.userId,
      logData.actionType,
      logData.entityType,
      logData.entityId,
      logData.details,
    ])

    return result.rows[0].id
  } catch (error) {
    console.error("Error creating activity log:", error)
    throw new Error("Failed to create activity log")
  }
}
