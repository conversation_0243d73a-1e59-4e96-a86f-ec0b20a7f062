"use server"

import { query } from "@/lib/db"

export type Subject = {
  id: number
  name: string
  description: string
  contentCount: number
  createdAt: string
}

export async function getSubjects(searchTerm = ""): Promise<Subject[]> {
  try {
    let sql = `
      SELECT 
        s.id,
        s.name,
        s.description,
        s.created_at as "createdAt",
        (
          SELECT COUNT(*) FROM (
            SELECT book_id FROM book_subjects WHERE subject_id = s.id
            UNION
            SELECT chapter_id FROM chapter_subjects WHERE subject_id = s.id
            UNION
            SELECT video_id FROM video_subjects WHERE subject_id = s.id
          ) as content_count
        ) as "contentCount"
      FROM subjects s
      WHERE 1=1
    `

    const params: any[] = []
    let paramIndex = 1

    if (searchTerm) {
      sql += ` AND (s.name ILIKE $${paramIndex} OR s.description ILIKE $${paramIndex})`
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    sql += ` ORDER BY s.name`

    const result = await query(sql, params)

    return result.rows.map((row) => ({
      ...row,
      createdAt: new Date(row.createdAt).toISOString(),
      contentCount: Number.parseInt(row.contentCount),
    }))
  } catch (error) {
    console.error("Error fetching subjects:", error)
    throw new Error("Failed to fetch subjects")
  }
}

export async function createSubject(subjectData: {
  name: string
  description: string
}) {
  try {
    const sql = `
      INSERT INTO subjects (name, description)
      VALUES ($1, $2)
      RETURNING id
    `

    const result = await query(sql, [subjectData.name, subjectData.description])
    return result.rows[0].id
  } catch (error) {
    console.error("Error creating subject:", error)
    throw new Error("Failed to create subject")
  }
}

export async function updateSubject(id: number, subjectData: Partial<Subject>) {
  try {
    const updates: string[] = []
    const values: any[] = []
    let paramIndex = 1

    if (subjectData.name !== undefined) {
      updates.push(`name = $${paramIndex++}`)
      values.push(subjectData.name)
    }

    if (subjectData.description !== undefined) {
      updates.push(`description = $${paramIndex++}`)
      values.push(subjectData.description)
    }

    values.push(id)

    const sql = `
      UPDATE subjects 
      SET ${updates.join(", ")} 
      WHERE id = $${paramIndex}
    `

    await query(sql, values)
    return true
  } catch (error) {
    console.error("Error updating subject:", error)
    throw new Error("Failed to update subject")
  }
}

export async function deleteSubject(id: number) {
  try {
    await query("DELETE FROM subjects WHERE id = $1", [id])
    return true
  } catch (error) {
    console.error("Error deleting subject:", error)
    throw new Error("Failed to delete subject")
  }
}
