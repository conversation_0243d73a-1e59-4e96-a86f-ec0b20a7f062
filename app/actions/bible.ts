"use server"

import { query } from "@/lib/db"
import { bibleAPI } from "@/lib/bible-api"

export async function linkVerseToChapter(chapterId: number, reference: string) {
  try {
    // Parse the reference
    const parsed = bibleAPI.parseReference(reference)
    if (!parsed) {
      throw new Error("Invalid Bible reference format")
    }

    // Get the verse content from the API
    const verseData = await bibleAPI.getVerse(reference)
    if (!verseData) {
      throw new Error("Could not fetch verse data")
    }

    // Start a transaction
    await query("BEGIN")

    // Insert or get the scripture reference
    const scriptureSql = `
      INSERT INTO scripture_references (book, chapter, verse_start, verse_end, reference_text)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (book, chapter, verse_start, verse_end) 
      DO UPDATE SET reference_text = EXCLUDED.reference_text
      RETURNING id
    `

    const scriptureResult = await query(scriptureSql, [
      parsed.book,
      parsed.chapter || 1,
      parsed.verse || 1,
      parsed.verse || 1,
      verseData.text,
    ])

    const scriptureId = scriptureResult.rows[0].id

    // Link to chapter
    await query("INSERT INTO chapter_scriptures (chapter_id, scripture_id) VALUES ($1, $2) ON CONFLICT DO NOTHING", [
      chapterId,
      scriptureId,
    ])

    // Commit the transaction
    await query("COMMIT")

    return { success: true, scriptureId }
  } catch (error) {
    // Rollback in case of error
    await query("ROLLBACK")
    console.error("Error linking verse to chapter:", error)
    throw new Error("Failed to link verse to chapter")
  }
}

export async function getChapterVerses(chapterId: number) {
  try {
    const sql = `
      SELECT 
        sr.id,
        sr.book,
        sr.chapter,
        sr.verse_start,
        sr.verse_end,
        sr.reference_text
      FROM 
        chapter_scriptures cs
      JOIN 
        scripture_references sr ON cs.scripture_id = sr.id
      WHERE 
        cs.chapter_id = $1
      ORDER BY 
        sr.book, sr.chapter, sr.verse_start
    `

    const result = await query(sql, [chapterId])
    return result.rows
  } catch (error) {
    console.error("Error fetching chapter verses:", error)
    throw new Error("Failed to fetch chapter verses")
  }
}

export async function searchBibleContent(searchTerm: string) {
  try {
    // Search in our local scripture references
    const sql = `
      SELECT 
        sr.id,
        sr.book,
        sr.chapter,
        sr.verse_start,
        sr.verse_end,
        sr.reference_text,
        COUNT(cs.chapter_id) as usage_count
      FROM 
        scripture_references sr
      LEFT JOIN 
        chapter_scriptures cs ON sr.id = cs.scripture_id
      WHERE 
        sr.reference_text ILIKE $1
        OR sr.book ILIKE $1
      GROUP BY 
        sr.id
      ORDER BY 
        usage_count DESC, sr.book, sr.chapter, sr.verse_start
      LIMIT 20
    `

    const result = await query(sql, [`%${searchTerm}%`])
    return result.rows
  } catch (error) {
    console.error("Error searching Bible content:", error)
    throw new Error("Failed to search Bible content")
  }
}
