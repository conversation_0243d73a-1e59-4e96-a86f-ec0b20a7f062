import type { Chapter } from "@/types/chapter"
import { isDatabaseConfigured } from "@/lib/database"
import { query } from "@/lib/db"

export async function getChapters(searchTerm = "", bookId = "all", status = "all"): Promise<Chapter[]> {
  if (!isDatabaseConfigured()) {
    throw new Error("Database is not properly configured. Please check your DATABASE_URL environment variable.")
  }

  try {
    // Server-side database query using PostgreSQL
    let sql = `
      SELECT 
        c.id, 
        c.title, 
        b.title as book,
        b.id as "bookId",
        u.full_name as author, 
        c.status, 
        c.word_count as "wordCount",
        c.updated_at as "lastUpdated",
        COALESCE(
          ARRAY_AGG(DISTINCT s.name) FILTER (WHERE s.name IS NOT NULL), 
          ARRAY[]::text[]
        ) as subjects,
        COALESCE(
          (
            SELECT STRING_AGG(sr.book || ' ' || sr.chapter || ':' || sr.verse_start, ', ')
            FROM chapter_scriptures cs
            JOIN scripture_references sr ON cs.scripture_id = sr.id
            WHERE cs.chapter_id = c.id
          ),
          ''
        ) as scripture
      FROM 
        chapters c
      JOIN 
        library_items b ON c.library_item_id = b.id
      LEFT JOIN 
        users u ON b.author_id = u.id
      LEFT JOIN 
        chapter_subjects cs ON c.id = cs.chapter_id
      LEFT JOIN 
        subjects s ON cs.subject_id = s.id
      WHERE 
        1=1
    `

    const params: any[] = []
    let paramIndex = 1

    if (searchTerm) {
      sql += ` AND (c.title ILIKE $${paramIndex} OR u.full_name ILIKE $${paramIndex} OR EXISTS (
        SELECT 1 FROM chapter_subjects cs2
        JOIN subjects s2 ON cs2.subject_id = s2.id
        WHERE cs2.chapter_id = c.id AND s2.name ILIKE $${paramIndex}
      ))`
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    if (bookId !== "all") {
      sql += ` AND b.id = $${paramIndex}`
      params.push(Number.parseInt(bookId))
      paramIndex++
    }

    if (status !== "all") {
      sql += ` AND c.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    sql += ` GROUP BY c.id, b.title, b.id, u.full_name ORDER BY c.updated_at DESC`

    const result = await query(sql, params)

    if (!result.rows) {
      return []
    }

    return result.rows.map((row) => ({
      ...row,
      lastUpdated: row.lastUpdated ? new Date(row.lastUpdated).toISOString().split("T")[0] : "",
      subjects: Array.isArray(row.subjects) ? row.subjects.filter(Boolean) : [],
      scripture: row.scripture || "",
    }))
  } catch (error) {
    console.error("Error fetching chapters:", error)
    throw new Error(`Failed to fetch chapters: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
