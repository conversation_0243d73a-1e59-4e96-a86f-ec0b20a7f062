"use server"

export type ExpositoryTerm = {
  id: number
  term: string
  definition: string
  category: string
  scriptureReference: string
  source: string
  minister: string
  createdAt: string
  tags: string[]
}

// Mock data for browser environment
const mockExpositoryTerms: ExpositoryTerm[] = [
  {
    id: 1,
    term: "Atonement",
    definition:
      "The reconciliation between God and humanity through the sacrificial death of <PERSON> Christ. It represents the covering or removal of sin, making peace between the holy God and sinful mankind.",
    category: "Theology",
    scriptureReference: "Romans 5:11",
    source: "Sunday Service - Redemption",
    minister: "<PERSON>",
    createdAt: "2023-01-01T00:00:00.000Z",
    tags: ["salvation", "sacrifice", "redemption"],
  },
  {
    id: 2,
    term: "Baptism",
    definition:
      "A Christian sacrament of admission and adoption, almost invariably with the use of water, into the Christian Church generally. It represents the believer's identification with Christ's death, burial, and resurrection.",
    category: "Biblical Terms",
    scriptureReference: "Romans 6:3-4",
    source: "Baptism Service",
    minister: "<PERSON>",
    createdAt: "2023-01-15T00:00:00.000Z",
    tags: ["sacrament", "water", "identification"],
  },
  {
    id: 3,
    term: "Consecration",
    definition:
      "The act of setting apart or dedicating something or someone for sacred use or divine service. It involves complete surrender and commitment to God's purposes.",
    category: "Spiritual Life",
    scriptureReference: "Romans 12:1",
    source: "Consecration Service",
    minister: "Pastor Johnson",
    createdAt: "2023-02-01T00:00:00.000Z",
    tags: ["dedication", "surrender", "service"],
  },
  {
    id: 4,
    term: "Discipleship",
    definition:
      "The process of following Jesus Christ and learning to live according to His teachings. It involves both learning from Christ and teaching others to do the same.",
    category: "Ministry",
    scriptureReference: "Matthew 28:19-20",
    source: "Discipleship Training",
    minister: "Minister Davis",
    createdAt: "2023-02-15T00:00:00.000Z",
    tags: ["following", "learning", "teaching"],
  },
  {
    id: 5,
    term: "Evangelism",
    definition:
      "The preaching or promulgation of the gospel; the work of an evangelist. It is the passionate proclamation of the good news of salvation through Jesus Christ.",
    category: "Ministry",
    scriptureReference: "Mark 16:15",
    source: "Evangelism Training",
    minister: "Pastor Williams",
    createdAt: "2023-03-01T00:00:00.000Z",
    tags: ["preaching", "gospel", "salvation"],
  },
  {
    id: 6,
    term: "Faith",
    definition:
      "Complete trust or confidence in God and His promises. It is the substance of things hoped for and the evidence of things not seen, enabling believers to please God.",
    category: "Theology",
    scriptureReference: "Hebrews 11:1",
    source: "Faith Series",
    minister: "Pastor Williams",
    createdAt: "2023-03-15T00:00:00.000Z",
    tags: ["trust", "confidence", "hope"],
  },
  {
    id: 7,
    term: "Grace",
    definition:
      "The unmerited favor and love of God toward humanity. It is God's kindness and mercy shown to those who do not deserve it, providing salvation freely through Christ.",
    category: "Theology",
    scriptureReference: "Ephesians 2:8-9",
    source: "Grace and Mercy",
    minister: "Elder Thompson",
    createdAt: "2023-04-01T00:00:00.000Z",
    tags: ["favor", "mercy", "unmerited"],
  },
  {
    id: 8,
    term: "Holiness",
    definition:
      "The quality of being set apart for God's purposes; moral and spiritual perfection. It involves both positional holiness (being declared holy by God) and practical holiness (living a holy life).",
    category: "Spiritual Life",
    scriptureReference: "1 Peter 1:16",
    source: "Holiness Series",
    minister: "Pastor Johnson",
    createdAt: "2023-04-15T00:00:00.000Z",
    tags: ["set apart", "perfection", "purity"],
  },
  {
    id: 9,
    term: "Intercession",
    definition:
      "The act of praying on behalf of others; standing in the gap between God and those in need. It is a ministry of prayer that seeks God's intervention in the lives of others.",
    category: "Spiritual Life",
    scriptureReference: "1 Timothy 2:1",
    source: "Prayer Meeting",
    minister: "Minister Davis",
    createdAt: "2023-05-01T00:00:00.000Z",
    tags: ["prayer", "standing", "intervention"],
  },
  {
    id: 10,
    term: "Justification",
    definition:
      "The act of God declaring a sinner righteous through faith in Jesus Christ. It is a legal declaration that removes the guilt and penalty of sin, making the believer right with God.",
    category: "Theology",
    scriptureReference: "Romans 5:1",
    source: "Justification Study",
    minister: "Pastor Williams",
    createdAt: "2023-05-15T00:00:00.000Z",
    tags: ["righteous", "declaration", "legal"],
  },
]

export async function getExpositoryTerms(): Promise<ExpositoryTerm[]> {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return mockExpositoryTerms
    }

    // In a real implementation, you would have an expository_terms table
    // For now, return mock data
    return mockExpositoryTerms
  } catch (error) {
    console.error("Error fetching expository terms:", error)
    return mockExpositoryTerms
  }
}

export async function createExpositoryTerm(termData: {
  term: string
  definition: string
  category: string
  scriptureReference: string
  minister: string
  tags: string[]
}) {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return { success: true, id: Date.now() }
    }

    // In a real implementation, you would insert into expository_terms table
    return { success: true, id: Date.now() }
  } catch (error) {
    console.error("Error creating expository term:", error)
    throw new Error("Failed to create expository term")
  }
}
