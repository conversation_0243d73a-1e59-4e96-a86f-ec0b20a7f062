import type { LibraryItem } from "@/types/library"
import { isDatabaseConfigured } from "@/lib/database"
import { query } from "@/lib/db"

export async function getLibraryContent(
  searchTerm = "",
  type = "all",
  category = "all",
  status = "all",
  sortBy = "title",
  sortOrder: "asc" | "desc" = "asc",
): Promise<LibraryItem[]> {
  if (!isDatabaseConfigured()) {
    throw new Error("Database is not properly configured. Please check your DATABASE_URL environment variable.")
  }

  try {
    // Build the query with filters using PostgreSQL
    let sql = `
      SELECT 
        li.id, 
        li.title, 
        COALESCE(u.full_name, li.author) as author,
        li.type, 
        li.status, 
        li.category, 
        li.description, 
        COALESCE(li.cover_image_url, li.cover_image, '/placeholder.svg?height=400&width=300') as cover,
        li.updated_at as "lastUpdated",
        COALESCE(li.tags, ARRAY[]::text[]) as tags,
        li.minister,
        li.duration,
        li.published_date as "publishedDate",
        li.target_audience as "targetAudience",
        li.difficulty,
        li.estimated_time as "estimatedTime",
        COALESCE(li.in_faith_library, false) as "inFaithLibrary",
        COALESCE(li.in_app, false) as "inApp",
        li.event,
        li.paperback_link as "paperbackLink",
        li.ebook_link as "ebookLink",
        li.faith_library_link as "faithLibraryLink",
        li.amazon_link as "amazonLink",
        li.cover_image_url as "coverImageUrl",
        (SELECT COUNT(*) FROM chapters c WHERE c.library_item_id = li.id) as chapters
      FROM 
        library_items li
      LEFT JOIN 
        users u ON li.author_id = u.id
      WHERE 
        1=1
    `

    const params: any[] = []
    let paramIndex = 1

    if (searchTerm) {
      sql += ` AND (li.title ILIKE $${paramIndex} OR COALESCE(u.full_name, li.author) ILIKE $${paramIndex} OR li.description ILIKE $${paramIndex})`
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    if (type !== "all") {
      sql += ` AND li.type = $${paramIndex}`
      params.push(type)
      paramIndex++
    }

    if (category !== "all") {
      sql += ` AND li.category = $${paramIndex}`
      params.push(category)
      paramIndex++
    }

    if (status !== "all") {
      sql += ` AND li.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // Add sorting
    const validSortColumns = ["title", "author", "type", "status", "category", "lastUpdated"]
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : "title"

    if (sortColumn === "lastUpdated") {
      sql += ` ORDER BY li.updated_at ${sortOrder === "desc" ? "DESC" : "ASC"}`
    } else if (sortColumn === "author") {
      sql += ` ORDER BY COALESCE(u.full_name, li.author) ${sortOrder === "desc" ? "DESC" : "ASC"}`
    } else {
      sql += ` ORDER BY li.${sortColumn} ${sortOrder === "desc" ? "DESC" : "ASC"}`
    }

    const result = await query(sql, params)

    if (!result.rows) {
      return []
    }

    return result.rows.map((row) => ({
      ...row,
      lastUpdated: row.lastUpdated ? new Date(row.lastUpdated).toISOString().split("T")[0] : "",
      tags: Array.isArray(row.tags) ? row.tags : [],
      chapters: Number.parseInt(row.chapters) || 0,
      publishedDate: row.publishedDate ? new Date(row.publishedDate).toISOString().split("T")[0] : undefined,
      inFaithLibrary: Boolean(row.inFaithLibrary),
      inApp: Boolean(row.inApp),
    }))
  } catch (error) {
    console.error("Error fetching library content:", error)
    throw new Error(`Failed to fetch library content: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
