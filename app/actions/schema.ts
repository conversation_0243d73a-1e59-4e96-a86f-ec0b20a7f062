import { z } from "zod"

export const BookSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title must be less than 255 characters"),
  author: z.string().min(1, "Author is required").max(255, "Author must be less than 255 characters"),
  genre: z.string().min(1, "Genre is required").max(100, "Genre must be less than 100 characters"),
})

export const ChapterSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title must be less than 255 characters"),
  content: z.string().optional(),
  bookId: z.number().int().positive("Book ID must be a positive integer"),
  status: z.enum(["Draft", "Published", "Archived"]).default("Draft"),
  subjects: z.array(z.string()).optional().default([]),
  scriptureReferences: z
    .array(
      z.object({
        book: z.string().min(1, "Book name is required"),
        chapter: z.number().int().positive("Chapter must be a positive integer"),
        verseStart: z.number().int().positive("Verse start must be a positive integer"),
        verseEnd: z.number().int().positive("Verse end must be a positive integer").optional(),
        text: z.string().optional(),
      }),
    )
    .optional()
    .default([]),
})

export const LibraryItemSchema = z.object({
  title: z.string().min(1, "Title is required").max(500, "Title must be less than 500 characters"),
  author: z.string().min(1, "Author is required").max(255, "Author must be less than 255 characters"),
  type: z.enum(["Book", "Study Guide", "Transcript", "Course"]),
  status: z.enum(["Draft", "Published", "Archived"]).default("Draft"),
  category: z.string().max(255, "Category must be less than 255 characters").optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional().default([]),

  // Transcript-specific fields
  minister: z.string().max(255, "Minister name must be less than 255 characters").optional(),
  duration: z.string().max(20, "Duration must be less than 20 characters").optional(),
  publishedDate: z.string().optional(),

  // Study Guide specific fields
  targetAudience: z.string().max(255, "Target audience must be less than 255 characters").optional(),
  difficulty: z.enum(["Beginner", "Intermediate", "Advanced"]).optional(),
  estimatedTime: z.string().max(100, "Estimated time must be less than 100 characters").optional(),
  inFaithLibrary: z.boolean().default(false),

  // Additional metadata
  inApp: z.boolean().default(false),
  event: z.string().max(255, "Event must be less than 255 characters").optional(),

  // Purchase and access links
  paperbackLink: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  ebookLink: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  faithLibraryLink: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  amazonLink: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  coverImageUrl: z.string().url("Must be a valid URL").optional().or(z.literal("")),
})

export const UserSchema = z.object({
  username: z
    .string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be less than 50 characters"),
  email: z.string().email("Must be a valid email address").max(255, "Email must be less than 255 characters"),
  fullName: z.string().min(1, "Full name is required").max(255, "Full name must be less than 255 characters"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters")
    .max(255, "Password must be less than 255 characters"),
  role: z.enum(["admin", "editor", "author", "user"]).default("user"),
})

export const SubjectSchema = z.object({
  name: z.string().min(1, "Subject name is required").max(255, "Subject name must be less than 255 characters"),
  description: z.string().optional(),
})

export type InputType = z.infer<typeof BookSchema>
export type ChapterInput = z.infer<typeof ChapterSchema>
export type LibraryItemInput = z.infer<typeof LibraryItemSchema>
export type UserInput = z.infer<typeof UserSchema>
export type SubjectInput = z.infer<typeof SubjectSchema>
