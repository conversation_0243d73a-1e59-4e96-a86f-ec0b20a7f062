"use server"

import { query } from "@/lib/db"

export type Subject = {
  id: number
  name: string
  description: string
  contentCount: number
  bookCount: number
  chapterCount: number
  videoCount: number
  createdAt: string
}

// Mock data for browser environment
const mockSubjects: Subject[] = [
  {
    id: 1,
    name: "Faith",
    description: "Topics related to faith and belief in God",
    contentCount: 45,
    bookCount: 12,
    chapterCount: 28,
    videoCount: 5,
    createdAt: "2023-01-01T00:00:00.000Z",
  },
  {
    id: 2,
    name: "Prayer",
    description: "Topics related to prayer and communication with God",
    contentCount: 32,
    bookCount: 8,
    chapterCount: 18,
    videoCount: 6,
    createdAt: "2023-01-15T00:00:00.000Z",
  },
  {
    id: 3,
    name: "Holiness",
    description: "Topics related to living a holy life",
    contentCount: 28,
    bookCount: 6,
    chapterCount: 15,
    videoCount: 7,
    createdAt: "2023-02-01T00:00:00.000Z",
  },
  {
    id: 4,
    name: "<PERSON>",
    description: "Biblical love and its expressions",
    contentCount: 24,
    bookCount: 5,
    chapterCount: 12,
    videoCount: 7,
    createdAt: "2023-02-15T00:00:00.000Z",
  },
  {
    id: 5,
    name: "Leadership",
    description: "Christian leadership principles",
    contentCount: 18,
    bookCount: 4,
    chapterCount: 10,
    videoCount: 4,
    createdAt: "2023-03-01T00:00:00.000Z",
  },
  {
    id: 6,
    name: "Prophecy",
    description: "Topics related to prophecy and prophetic ministry",
    contentCount: 15,
    bookCount: 3,
    chapterCount: 8,
    videoCount: 4,
    createdAt: "2023-03-15T00:00:00.000Z",
  },
]

export async function getSubjects(): Promise<Subject[]> {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return mockSubjects
    }

    const sql = `
      SELECT 
        s.id,
        s.name,
        s.description,
        s.created_at as "createdAt",
        COALESCE(book_count.count, 0) as "bookCount",
        COALESCE(chapter_count.count, 0) as "chapterCount",
        COALESCE(video_count.count, 0) as "videoCount",
        (COALESCE(book_count.count, 0) + COALESCE(chapter_count.count, 0) + COALESCE(video_count.count, 0)) as "contentCount"
      FROM subjects s
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM book_subjects
        GROUP BY subject_id
      ) book_count ON s.id = book_count.subject_id
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM chapter_subjects
        GROUP BY subject_id
      ) chapter_count ON s.id = chapter_count.subject_id
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM video_subjects
        GROUP BY subject_id
      ) video_count ON s.id = video_count.subject_id
      ORDER BY s.name
    `

    const result = await query(sql)

    return result.rows.map((row) => ({
      ...row,
      createdAt: new Date(row.createdAt).toISOString(),
      bookCount: Number.parseInt(row.bookCount),
      chapterCount: Number.parseInt(row.chapterCount),
      videoCount: Number.parseInt(row.videoCount),
      contentCount: Number.parseInt(row.contentCount),
    }))
  } catch (error) {
    console.error("Error fetching subjects:", error)
    return mockSubjects
  }
}

export async function createSubject(name: string, description: string) {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return { success: true, id: Date.now() }
    }

    const sql = `
      INSERT INTO subjects (name, description)
      VALUES ($1, $2)
      RETURNING id
    `

    const result = await query(sql, [name, description])
    return { success: true, id: result.rows[0].id }
  } catch (error) {
    console.error("Error creating subject:", error)
    throw new Error("Failed to create subject")
  }
}

export async function updateSubject(id: number, name: string, description: string) {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return { success: true }
    }

    const sql = `
      UPDATE subjects 
      SET name = $1, description = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `

    await query(sql, [name, description, id])
    return { success: true }
  } catch (error) {
    console.error("Error updating subject:", error)
    throw new Error("Failed to update subject")
  }
}

export async function deleteSubject(id: number) {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return { success: true }
    }

    // First, delete related records in junction tables
    await query("DELETE FROM book_subjects WHERE subject_id = $1", [id])
    await query("DELETE FROM chapter_subjects WHERE subject_id = $1", [id])
    await query("DELETE FROM video_subjects WHERE subject_id = $1", [id])

    // Then delete the subject itself
    const sql = "DELETE FROM subjects WHERE id = $1"
    await query(sql, [id])

    return { success: true }
  } catch (error) {
    console.error("Error deleting subject:", error)
    throw new Error("Failed to delete subject")
  }
}

export async function getSubjectById(id: number): Promise<Subject | null> {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return mockSubjects.find((s) => s.id === id) || null
    }

    const sql = `
      SELECT 
        s.id,
        s.name,
        s.description,
        s.created_at as "createdAt",
        COALESCE(book_count.count, 0) as "bookCount",
        COALESCE(chapter_count.count, 0) as "chapterCount",
        COALESCE(video_count.count, 0) as "videoCount",
        (COALESCE(book_count.count, 0) + COALESCE(chapter_count.count, 0) + COALESCE(video_count.count, 0)) as "contentCount"
      FROM subjects s
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM book_subjects
        GROUP BY subject_id
      ) book_count ON s.id = book_count.subject_id
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM chapter_subjects
        GROUP BY subject_id
      ) chapter_count ON s.id = chapter_count.subject_id
      LEFT JOIN (
        SELECT subject_id, COUNT(*) as count
        FROM video_subjects
        GROUP BY subject_id
      ) video_count ON s.id = video_count.subject_id
      WHERE s.id = $1
    `

    const result = await query(sql, [id])

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      ...row,
      createdAt: new Date(row.createdAt).toISOString(),
      bookCount: Number.parseInt(row.bookCount),
      chapterCount: Number.parseInt(row.chapterCount),
      videoCount: Number.parseInt(row.videoCount),
      contentCount: Number.parseInt(row.contentCount),
    }
  } catch (error) {
    console.error("Error fetching subject by ID:", error)
    return mockSubjects.find((s) => s.id === id) || null
  }
}
