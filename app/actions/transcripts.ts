"use server"

import { query } from "@/lib/db"

export type Transcript = {
  id: number
  title: string
  videoTitle: string
  minister: string
  duration: string
  publishedDate: string
  status: "completed" | "in-progress" | "pending"
  progress: number
  content: string
  isEdited: boolean
  createdAt: string
}

// Mock data for browser environment
const mockTranscripts: Transcript[] = [
  {
    id: 1,
    title: "Sunday Service - The Power of Faith",
    videoTitle: "Sunday Service - The Power of Faith",
    minister: "<PERSON>",
    duration: "45:32",
    publishedDate: "2024-01-14",
    status: "completed",
    progress: 100,
    content:
      "Welcome to our Sunday service. Today we're going to talk about the power of faith and how it transforms our lives. Faith is not just believing in something, but it's acting on what we believe. When we have faith, we can move mountains...",
    isEdited: true,
    createdAt: "2024-01-15T10:00:00.000Z",
  },
  {
    id: 2,
    title: "Midweek Study - Biblical Holiness",
    videoTitle: "Midweek Study - Biblical Holiness",
    minister: "<PERSON>",
    duration: "32:18",
    publishedDate: "2024-01-12",
    status: "in-progress",
    progress: 75,
    content:
      "Tonight we're diving deep into what it means to live a holy life according to <PERSON>rip<PERSON>. Holiness is not about perfection, but about being set apart for God's purposes...",
    isEdited: false,
    createdAt: "2024-01-13T19:00:00.000Z",
  },
  {
    id: 3,
    title: "Youth Service - Walking in Love",
    videoTitle: "Youth Service - Walking in Love",
    minister: "Pastor Johnson",
    duration: "28:45",
    publishedDate: "2024-01-10",
    status: "pending",
    progress: 0,
    content:
      "Young people, tonight we're talking about what it means to walk in love. Love is not just a feeling, it's an action...",
    isEdited: false,
    createdAt: "2024-01-11T18:30:00.000Z",
  },
  {
    id: 4,
    title: "Prayer Meeting - Intercession",
    videoTitle: "Prayer Meeting - The Power of Intercession",
    minister: "Minister Davis",
    duration: "52:15",
    publishedDate: "2024-01-08",
    status: "completed",
    progress: 100,
    content:
      "Tonight we're focusing on the ministry of intercession. Intercession is standing in the gap for others, praying on their behalf...",
    isEdited: true,
    createdAt: "2024-01-09T20:00:00.000Z",
  },
  {
    id: 5,
    title: "Bible Study - Spiritual Warfare",
    videoTitle: "Bible Study - Understanding Spiritual Warfare",
    minister: "Pastor Williams",
    duration: "41:22",
    publishedDate: "2024-01-05",
    status: "in-progress",
    progress: 60,
    content:
      "We're in a spiritual battle every day. Understanding the nature of this warfare is crucial for every believer...",
    isEdited: false,
    createdAt: "2024-01-06T19:30:00.000Z",
  },
]

export async function getTranscripts(): Promise<Transcript[]> {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return mockTranscripts
    }

    const sql = `
      SELECT 
        t.id,
        t.content,
        t.is_edited as "isEdited",
        t.created_at as "createdAt",
        v.title as "videoTitle",
        v.duration,
        v.published_date as "publishedDate",
        v.transcription_status as status,
        v.transcription_progress as progress,
        u.full_name as minister
      FROM transcripts t
      JOIN videos v ON t.video_id = v.id
      JOIN users u ON v.minister_id = u.id
      ORDER BY v.published_date DESC
    `

    const result = await query(sql)

    return result.rows.map((row) => ({
      ...row,
      title: row.videoTitle,
      createdAt: new Date(row.createdAt).toISOString(),
      publishedDate: new Date(row.publishedDate).toISOString().split("T")[0],
    }))
  } catch (error) {
    console.error("Error fetching transcripts:", error)
    return mockTranscripts
  }
}

export async function createTranscript(transcriptData: {
  videoTitle: string
  minister: string
  content: string
}) {
  try {
    const isBrowser = typeof window !== "undefined"

    if (isBrowser) {
      return { success: true, id: Date.now() }
    }

    // In a real implementation, you would create the video and transcript
    // For now, we'll just return success
    return { success: true, id: Date.now() }
  } catch (error) {
    console.error("Error creating transcript:", error)
    throw new Error("Failed to create transcript")
  }
}
