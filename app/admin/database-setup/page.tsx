"use client"

import { useState, useEffect } from "react"

export default function DatabaseSetupPage() {
  const [status, setStatus] = useState("idle")
  const [message, setMessage] = useState("")

  useEffect(() => {
    async function checkDatabaseConnection() {
      setStatus("checking")
      setMessage("Checking Neon PostgreSQL connection...")

      try {
        const response = await fetch("/api/health")
        const result = await response.json()

        if (result.success) {
          setStatus("success")
          setMessage("Successfully connected to Neon PostgreSQL!")
        } else {
          setStatus("error")
          setMessage(`Database connection failed: ${result.error}`)
        }
      } catch (e: any) {
        setStatus("error")
        setMessage(`Error during connection check: ${e.message}`)
      }
    }

    checkDatabaseConnection()
  }, [])

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Database Setup</h1>
      <p>This page checks the connection to the Neon PostgreSQL database.</p>

      {status === "checking" && (
        <div className="mt-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
            <span>{message}</span>
          </div>
        </div>
      )}

      {status === "success" && (
        <div className="mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            <span>{message}</span>
          </div>
        </div>
      )}

      {status === "error" && (
        <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span>{message}</span>
          </div>
        </div>
      )}
    </div>
  )
}
