"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Database, Download, Upload, RefreshCw, AlertTriangle, CheckCircle, Server } from "lucide-react"

export default function DatabasePage() {
  const [backupProgress, setBackupProgress] = useState(0)
  const [isBackingUp, setIsBackingUp] = useState(false)

  const handleBackup = async () => {
    setIsBackingUp(true)
    setBackupProgress(0)

    // Simulate backup progress
    for (let i = 0; i <= 100; i += 10) {
      setBackupProgress(i)
      await new Promise((resolve) => setTimeout(resolve, 200))
    }

    setIsBackingUp(false)
  }

  const dbStats = {
    totalSize: "2.4 GB",
    tables: 12,
    records: 15847,
    lastBackup: "2 hours ago",
    status: "healthy",
  }

  const tables = [
    { name: "users", records: 127, size: "45 MB", status: "healthy" },
    { name: "books", records: 89, size: "234 MB", status: "healthy" },
    { name: "chapters", records: 2847, size: "1.2 GB", status: "healthy" },
    { name: "videos", records: 456, size: "89 MB", status: "healthy" },
    { name: "subjects", records: 234, size: "12 MB", status: "healthy" },
    { name: "workspaces", records: 67, size: "156 MB", status: "healthy" },
    { name: "transcripts", records: 456, size: "345 MB", status: "healthy" },
    { name: "scripture_references", records: 1234, size: "67 MB", status: "healthy" },
    { name: "activity_logs", records: 9876, size: "234 MB", status: "healthy" },
    { name: "drafts", records: 234, size: "89 MB", status: "healthy" },
    { name: "tasks", records: 123, size: "23 MB", status: "healthy" },
    { name: "study_guides", records: 156, size: "178 MB", status: "healthy" },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Database Management</h1>
        <p className="text-gray-600">Monitor and manage your database</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database Size</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dbStats.totalSize}</div>
            <Progress value={24} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">24% of 10 GB limit</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tables</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dbStats.tables}</div>
            <p className="text-xs text-muted-foreground">All tables healthy</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dbStats.records.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across all tables</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Backup</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dbStats.lastBackup}</div>
            <p className="text-xs text-muted-foreground">Automatic backup</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="backup">Backup & Restore</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Database Tables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tables.map((table) => (
                  <div key={table.name} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <Database className="w-5 h-5 text-blue-600" />
                      <div>
                        <p className="font-medium">{table.name}</p>
                        <p className="text-sm text-gray-500">{table.records.toLocaleString()} records</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">{table.size}</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        {table.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backup">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="w-5 h-5 mr-2" />
                  Create Backup
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>Creating a backup will temporarily lock the database for writes.</AlertDescription>
                </Alert>

                {isBackingUp && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Creating backup...</span>
                      <span className="text-sm">{backupProgress}%</span>
                    </div>
                    <Progress value={backupProgress} />
                  </div>
                )}

                <Button onClick={handleBackup} disabled={isBackingUp} className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  {isBackingUp ? "Creating Backup..." : "Create Full Backup"}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="w-5 h-5 mr-2" />
                  Restore Database
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Restoring will overwrite all current data. This action cannot be undone.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Select Backup File</label>
                  <input type="file" accept=".sql,.backup" className="w-full p-2 border rounded" />
                </div>

                <Button variant="destructive" className="w-full">
                  <Upload className="w-4 h-4 mr-2" />
                  Restore Database
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Backups</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { name: "backup_2024_01_15_14_30.sql", size: "2.4 GB", date: "2 hours ago" },
                  { name: "backup_2024_01_15_08_00.sql", size: "2.3 GB", date: "8 hours ago" },
                  { name: "backup_2024_01_14_20_00.sql", size: "2.3 GB", date: "Yesterday" },
                  { name: "backup_2024_01_14_08_00.sql", size: "2.2 GB", date: "2 days ago" },
                ].map((backup, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium text-sm">{backup.name}</p>
                      <p className="text-xs text-gray-500">
                        {backup.size} • {backup.date}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Download className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <RefreshCw className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle>Database Maintenance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="h-20 flex-col space-y-2">
                  <RefreshCw className="w-5 h-5" />
                  <span>Optimize Tables</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2">
                  <Database className="w-5 h-5" />
                  <span>Rebuild Indexes</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2">
                  <AlertTriangle className="w-5 h-5" />
                  <span>Check Integrity</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2">
                  <RefreshCw className="w-5 h-5" />
                  <span>Update Statistics</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring">
          <Card>
            <CardHeader>
              <CardTitle>Database Monitoring</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Real-time monitoring dashboard coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
