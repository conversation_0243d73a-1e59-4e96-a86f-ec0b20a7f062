"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Upload, FileText, AlertCircle, CheckCircle, Download } from "lucide-react"

export default function FileMakerImportPage() {
  const [importing, setImporting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [importResults, setImportResults] = useState<any>(null)
  const [jsonData, setJsonData] = useState("")

  const handleFileMakerImport = async () => {
    if (!jsonData.trim()) {
      alert("Please paste your FileMaker JSON data first")
      return
    }

    setImporting(true)
    setProgress(0)
    setImportResults(null)

    try {
      // Parse JSON data
      const data = JSON.parse(jsonData)

      // Simulate progress
      setProgress(25)

      // Send to import API
      const response = await fetch("/api/import/filemaker", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      setProgress(75)

      const result = await response.json()

      setProgress(100)
      setImportResults(result)
    } catch (error) {
      console.error("Import error:", error)
      setImportResults({
        success: false,
        error: error instanceof Error ? error.message : "Import failed",
      })
    } finally {
      setImporting(false)
    }
  }

  const downloadSampleScript = () => {
    const script = `# FileMaker Script: Export Books to JSON
# This script exports all books from your FileMaker database to JSON format

# Step 1: Go to Layout (Books)
Go to Layout [ "Books" ]

# Step 2: Show All Records
Show All Records

# Step 3: Sort Records (optional)
Sort Records [ Specified Sort Order: Books::Title; ascending ]

# Step 4: Export Records
Export Records [ No dialog; "books_export.json"; Unicode (UTF-8) ]
# Export format: JSON
# Include field names as: Field names as JSON object names

# Field Mapping for Export:
# - id → Books::ID
# - title → Books::Title  
# - author → Books::Author
# - type → Books::Type
# - status → Books::Status
# - category → Books::Category
# - description → Books::Description
# - subjects → Books::Subjects
# - chapters → Books::ChapterCount
# - createdDate → Books::DateCreated
# - modifiedDate → Books::DateModified
# - minister → Books::Minister
# - event → Books::Event
# - yearPublished → Books::YearPublished
# - isInFaithLibrary → Books::InFaithLibrary

# Step 5: Show Custom Dialog
Show Custom Dialog [ "Export Complete"; "Books have been exported to books_export.json. Upload this file to your web application." ]`

    const blob = new Blob([script], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "filemaker_export_script.txt"
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">FileMaker Import</h1>
        <p className="text-gray-600">Import books from your FileMaker database</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              FileMaker Export Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>Follow these steps to export your books from FileMaker Pro</AlertDescription>
            </Alert>

            <div className="space-y-3">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold">Step 1: Download Script</h4>
                <p className="text-sm text-gray-600">Download the FileMaker script template</p>
                <Button variant="outline" size="sm" onClick={downloadSampleScript} className="mt-2">
                  <Download className="w-4 h-4 mr-2" />
                  Download Script
                </Button>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold">Step 2: Run Script</h4>
                <p className="text-sm text-gray-600">
                  Import and run the script in FileMaker Pro to export your books as JSON
                </p>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold">Step 3: Copy JSON</h4>
                <p className="text-sm text-gray-600">
                  Open the exported JSON file and copy its contents to the text area below
                </p>
              </div>
            </div>

            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                The import will automatically map FileMaker fields to the new database structure
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Import Interface */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              Import Data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="json-data" className="block text-sm font-medium text-gray-700 mb-2">
                Paste FileMaker JSON Export
              </label>
              <Textarea
                id="json-data"
                value={jsonData}
                onChange={(e) => setJsonData(e.target.value)}
                placeholder='{"books": [{"title": "Book Title", "author": "Author Name", ...}]}'
                rows={12}
                disabled={importing}
                className="font-mono text-sm"
              />
            </div>

            {importing && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Importing books...</span>
                  <span className="text-sm text-gray-600">{progress}%</span>
                </div>
                <Progress value={progress} />
              </div>
            )}

            <Button onClick={handleFileMakerImport} disabled={importing || !jsonData.trim()} className="w-full">
              <Upload className="w-4 h-4 mr-2" />
              {importing ? "Importing..." : "Import from FileMaker"}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Results */}
      {importResults && (
        <Card>
          <CardHeader>
            <CardTitle>Import Results</CardTitle>
          </CardHeader>
          <CardContent>
            {importResults.success ? (
              <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{importResults.results?.imported || 0}</div>
                    <div className="text-sm text-gray-600">Imported</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{importResults.results?.warnings || 0}</div>
                    <div className="text-sm text-gray-600">Updated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{importResults.results?.errors || 0}</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </div>
                </div>

                {importResults.results?.details && (
                  <div>
                    <h4 className="font-semibold mb-2">Details:</h4>
                    <div className="bg-gray-50 p-3 rounded max-h-40 overflow-y-auto">
                      {importResults.results.details.map((detail: string, index: number) => (
                        <div key={index} className="text-sm text-gray-700 mb-1">
                          {detail}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>Import failed: {importResults.error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
