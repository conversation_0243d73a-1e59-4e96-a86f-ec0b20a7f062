"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Upload, FileText, BookOpen, Tags, Download, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function ImportPage() {
  const [importing, setImporting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [importResults, setImportResults] = useState<any>(null)

  const handleBooksImport = async (formData: FormData) => {
    setImporting(true)
    setProgress(0)

    try {
      // Simulate import progress
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise((resolve) => setTimeout(resolve, 200))
      }

      setImportResults({
        type: "books",
        imported: 25,
        errors: 2,
        warnings: 1,
      })
    } catch (error) {
      console.error("Import error:", error)
    } finally {
      setImporting(false)
    }
  }

  const handleSubjectsImport = async (formData: FormData) => {
    setImporting(true)
    setProgress(0)

    try {
      // Simulate import progress
      for (let i = 0; i <= 100; i += 20) {
        setProgress(i)
        await new Promise((resolve) => setTimeout(resolve, 150))
      }

      setImportResults({
        type: "subjects",
        imported: 45,
        errors: 0,
        warnings: 3,
      })
    } catch (error) {
      console.error("Import error:", error)
    } finally {
      setImporting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Import Tools</h1>
        <p className="text-gray-600">Import books, chapters, subjects, and other content</p>
      </div>

      <Tabs defaultValue="books" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="books">Books</TabsTrigger>
          <TabsTrigger value="chapters">Chapters</TabsTrigger>
          <TabsTrigger value="subjects">Subjects</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Import</TabsTrigger>
        </TabsList>

        <TabsContent value="books">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Import Books
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Upload a JSON file with book data. Each book should include title, author, type, category, and
                  description.
                </AlertDescription>
              </Alert>

              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  const formData = new FormData(e.currentTarget)
                  handleBooksImport(formData)
                }}
              >
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="books-file">JSON File</Label>
                    <Input id="books-file" name="file" type="file" accept=".json" required disabled={importing} />
                  </div>

                  <div>
                    <Label htmlFor="books-preview">Preview/Paste JSON</Label>
                    <Textarea
                      id="books-preview"
                      name="preview"
                      placeholder="Paste JSON data here to preview..."
                      rows={8}
                      disabled={importing}
                    />
                  </div>

                  {importing && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Importing books...</span>
                        <span className="text-sm text-gray-600">{progress}%</span>
                      </div>
                      <Progress value={progress} />
                    </div>
                  )}

                  <Button type="submit" disabled={importing} className="w-full">
                    <Upload className="w-4 h-4 mr-2" />
                    {importing ? "Importing..." : "Import Books"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chapters">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Import Chapters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Upload chapters with content, book associations, and scripture references.
                </AlertDescription>
              </Alert>

              <form>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="chapters-file">JSON File</Label>
                    <Input id="chapters-file" type="file" accept=".json" disabled={importing} />
                  </div>

                  <div>
                    <Label htmlFor="chapters-preview">Preview/Paste JSON</Label>
                    <Textarea
                      id="chapters-preview"
                      placeholder="Paste chapter JSON data here..."
                      rows={8}
                      disabled={importing}
                    />
                  </div>

                  <Button type="submit" disabled={importing} className="w-full">
                    <Upload className="w-4 h-4 mr-2" />
                    Import Chapters
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tags className="w-5 h-5 mr-2" />
                Import Subjects
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Import subject tags for categorizing content. Supports bulk import with descriptions.
                </AlertDescription>
              </Alert>

              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  const formData = new FormData(e.currentTarget)
                  handleSubjectsImport(formData)
                }}
              >
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="subjects-file">JSON File</Label>
                    <Input id="subjects-file" name="file" type="file" accept=".json" disabled={importing} />
                  </div>

                  <div>
                    <Label htmlFor="subjects-preview">Preview/Paste JSON</Label>
                    <Textarea
                      id="subjects-preview"
                      name="preview"
                      placeholder='[{"name": "Faith", "description": "Topics related to faith and belief"}, ...]'
                      rows={8}
                      disabled={importing}
                    />
                  </div>

                  {importing && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Importing subjects...</span>
                        <span className="text-sm text-gray-600">{progress}%</span>
                      </div>
                      <Progress value={progress} />
                    </div>
                  )}

                  <Button type="submit" disabled={importing} className="w-full">
                    <Upload className="w-4 h-4 mr-2" />
                    {importing ? "Importing..." : "Import Subjects"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Download className="w-5 h-5 mr-2" />
                Bulk Operations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Export Data</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Export All Books
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Export All Chapters
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Export All Subjects
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Templates</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Book Template
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Chapter Template
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Subject Template
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {importResults && (
        <Card>
          <CardHeader>
            <CardTitle>Import Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Successfully imported:</span>
                <span className="font-medium text-green-600">{importResults.imported} items</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Errors:</span>
                <span className="font-medium text-red-600">{importResults.errors} items</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Warnings:</span>
                <span className="font-medium text-yellow-600">{importResults.warnings} items</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
