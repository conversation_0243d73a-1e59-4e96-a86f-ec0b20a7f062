"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Download, Activity, User, BookOpen, Video, Settings } from "lucide-react"

export default function ActivityLogsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [actionFilter, setActionFilter] = useState("all")
  const [userFilter, setUserFilter] = useState("all")

  const activityLogs = [
    {
      id: 1,
      user: "<PERSON>",
      action: "user_login",
      description: "User logged in",
      timestamp: "2024-01-15 14:30:25",
      ip: "*************",
      userAgent: "Chrome 120.0",
      icon: User,
      color: "text-blue-600",
    },
    {
      id: 2,
      user: "Admin",
      action: "book_created",
      description: "Created new book: 'Foundations of Faith'",
      timestamp: "2024-01-15 14:25:12",
      ip: "*************",
      userAgent: "Chrome 120.0",
      icon: BookOpen,
      color: "text-green-600",
    },
    {
      id: 3,
      user: "Jane Doe",
      action: "video_uploaded",
      description: "Uploaded video: 'Sunday Service - Faith'",
      timestamp: "2024-01-15 14:20:45",
      ip: "*************",
      userAgent: "Firefox 121.0",
      icon: Video,
      color: "text-purple-600",
    },
    {
      id: 4,
      user: "Admin",
      action: "user_created",
      description: "Created new user: <EMAIL>",
      timestamp: "2024-01-15 14:15:33",
      ip: "*************",
      userAgent: "Chrome 120.0",
      icon: User,
      color: "text-blue-600",
    },
    {
      id: 5,
      user: "Editor",
      action: "chapter_updated",
      description: "Updated chapter: 'Walking by Faith'",
      timestamp: "2024-01-15 14:10:18",
      ip: "*************",
      userAgent: "Safari 17.0",
      icon: BookOpen,
      color: "text-orange-600",
    },
    {
      id: 6,
      user: "Admin",
      action: "settings_changed",
      description: "Updated AI assistant configuration",
      timestamp: "2024-01-15 14:05:07",
      ip: "*************",
      userAgent: "Chrome 120.0",
      icon: Settings,
      color: "text-gray-600",
    },
  ]

  const filteredLogs = activityLogs.filter((log) => {
    const matchesSearch =
      log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAction = actionFilter === "all" || log.action.includes(actionFilter)
    const matchesUser = userFilter === "all" || log.user === userFilter

    return matchesSearch && matchesAction && matchesUser
  })

  const getActionBadgeColor = (action: string) => {
    if (action.includes("login")) return "bg-blue-50 text-blue-700 border-blue-200"
    if (action.includes("created")) return "bg-green-50 text-green-700 border-green-200"
    if (action.includes("updated")) return "bg-orange-50 text-orange-700 border-orange-200"
    if (action.includes("deleted")) return "bg-red-50 text-red-700 border-red-200"
    return "bg-gray-50 text-gray-700 border-gray-200"
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activity Logs</h1>
          <p className="text-gray-600">Monitor user actions and system events</p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Logs
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,847</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">User Logins</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Content Changes</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">456</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Events</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Recent Activity
          </CardTitle>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="login">Logins</SelectItem>
                <SelectItem value="created">Created</SelectItem>
                <SelectItem value="updated">Updated</SelectItem>
                <SelectItem value="deleted">Deleted</SelectItem>
              </SelectContent>
            </Select>

            <Select value={userFilter} onValueChange={setUserFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="User" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Editor">Editor</SelectItem>
                <SelectItem value="John Smith">John Smith</SelectItem>
                <SelectItem value="Jane Doe">Jane Doe</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>User Agent</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.map((log) => {
                const Icon = log.icon
                return (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Icon className={`w-4 h-4 ${log.color}`} />
                        <Badge variant="outline" className={getActionBadgeColor(log.action)}>
                          {log.action.replace("_", " ")}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">{log.user}</TableCell>
                    <TableCell>{log.description}</TableCell>
                    <TableCell className="text-sm text-gray-500">{log.timestamp}</TableCell>
                    <TableCell className="text-sm text-gray-500">{log.ip}</TableCell>
                    <TableCell className="text-sm text-gray-500">{log.userAgent}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
