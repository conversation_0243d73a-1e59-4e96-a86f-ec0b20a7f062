"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Search, Plus, Edit, Trash2, Tags, BookOpen, Video } from "lucide-react"
import { getSubjects, createSubject, deleteSubject } from "@/app/actions/subjects"

interface Subject {
  id: number
  name: string
  description: string
  contentCount: number
  createdAt: string
}

export default function SubjectsPage() {
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddModal, setShowAddModal] = useState(false)
  const [newSubject, setNewSubject] = useState({ name: "", description: "" })

  useEffect(() => {
    fetchSubjects()
  }, [])

  const fetchSubjects = async () => {
    try {
      setLoading(true)
      const data = await getSubjects()
      setSubjects(data)
    } catch (error) {
      console.error("Error fetching subjects:", error)
      // Fallback to mock data if database fails
      setSubjects([
        {
          id: 1,
          name: "Faith",
          description: "Teaching about faith and trust in God",
          contentCount: 127,
          createdAt: "2024-01-15T10:30:00Z",
        },
        {
          id: 2,
          name: "Prayer",
          description: "Lessons on prayer and communion with God",
          contentCount: 89,
          createdAt: "2024-01-20T14:15:00Z",
        },
        {
          id: 3,
          name: "Salvation",
          description: "Understanding salvation through Christ",
          contentCount: 156,
          createdAt: "2024-02-01T09:45:00Z",
        },
        {
          id: 4,
          name: "Holiness",
          description: "Living a holy life according to Scripture",
          contentCount: 98,
          createdAt: "2024-02-10T16:20:00Z",
        },
        {
          id: 5,
          name: "Love",
          description: "God's love and loving others",
          contentCount: 73,
          createdAt: "2024-02-15T11:30:00Z",
        },
        {
          id: 6,
          name: "Wisdom",
          description: "Biblical wisdom and understanding",
          contentCount: 45,
          createdAt: "2024-03-01T13:10:00Z",
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  const filteredSubjects = subjects.filter(
    (subject) =>
      subject.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subject.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddSubject = async () => {
    if (newSubject.name.trim()) {
      try {
        await createSubject(newSubject.name, newSubject.description)
        setNewSubject({ name: "", description: "" })
        setShowAddModal(false)
        fetchSubjects() // Refresh the list
      } catch (error) {
        console.error("Error creating subject:", error)
        // Fallback to local state update
        const subject = {
          id: Math.max(...subjects.map((s) => s.id)) + 1,
          name: newSubject.name,
          description: newSubject.description,
          contentCount: 0,
          createdAt: new Date().toISOString(),
        }
        setSubjects([...subjects, subject])
        setNewSubject({ name: "", description: "" })
        setShowAddModal(false)
      }
    }
  }

  const handleDeleteSubject = async (id: number) => {
    try {
      await deleteSubject(id)
      fetchSubjects() // Refresh the list
    } catch (error) {
      console.error("Error deleting subject:", error)
      // Fallback to local state update
      setSubjects(subjects.filter((s) => s.id !== id))
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Subjects Management</h1>
            <p className="text-gray-600">Manage content categorization tags and subjects</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subjects Management</h1>
          <p className="text-gray-600">Manage content categorization tags and subjects</p>
        </div>
        <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
          <DialogTrigger asChild>
            <Button className="bg-red-600 hover:bg-red-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Subject
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Subject</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="subject-name">Subject Name</Label>
                <Input
                  id="subject-name"
                  value={newSubject.name}
                  onChange={(e) => setNewSubject({ ...newSubject, name: e.target.value })}
                  placeholder="e.g., Faith, Prayer, Salvation"
                />
              </div>
              <div>
                <Label htmlFor="subject-description">Description</Label>
                <Textarea
                  id="subject-description"
                  value={newSubject.description}
                  onChange={(e) => setNewSubject({ ...newSubject, description: e.target.value })}
                  placeholder="Brief description of this subject..."
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowAddModal(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddSubject} className="bg-red-600 hover:bg-red-700">
                  Add Subject
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Subjects</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subjects.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Most Used</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Faith</div>
            <p className="text-sm text-gray-500">127 items</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recently Added</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-sm text-gray-500">This week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Unused</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-sm text-gray-500">No content</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Tags className="w-5 h-5 mr-2" />
            All Subjects
          </CardTitle>
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search subjects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Subject</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Content Count</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSubjects.map((subject) => (
                <TableRow key={subject.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{subject.name}</Badge>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-md">
                    <p className="text-sm text-gray-600 truncate">{subject.description}</p>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <BookOpen className="w-3 h-3 text-blue-600" />
                        <span className="text-sm">{Math.floor(subject.contentCount * 0.3)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Video className="w-3 h-3 text-purple-600" />
                        <span className="text-sm">{Math.floor(subject.contentCount * 0.7)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {new Date(subject.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-red-600"
                        onClick={() => handleDeleteSubject(subject.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
