import { NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"

export async function GET() {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "Database not configured" }, { status: 500 })
    }

    const sql = neon(process.env.DATABASE_URL)
    
    // Get basic content counts
    const [libraryItems, chapters, videos, users] = await Promise.all([
      sql`SELECT COUNT(*) as count FROM library_items`,
      sql`SELECT COUNT(*) as count FROM chapters`,
      sql`SELECT COUNT(*) as count FROM videos`,
      sql`SELECT COUNT(*) as count FROM users`
    ])

    // Get content by type
    const contentByType = await sql`
      SELECT type, COUNT(*) as count 
      FROM library_items 
      GROUP BY type 
      ORDER BY count DESC
    `

    // Get recent content (top 5 most recently updated)
    const topContent = await sql`
      SELECT 
        li.title,
        li.type,
        li.updated_at,
        COALESCE(c.chapter_count, 0) as chapters
      FROM library_items li
      LEFT JOIN (
        SELECT library_item_id, COUNT(*) as chapter_count
        FROM chapters
        GROUP BY library_item_id
      ) c ON li.id = c.library_item_id
      ORDER BY li.updated_at DESC
      LIMIT 5
    `

    // Get published vs draft stats
    const statusStats = await sql`
      SELECT status, COUNT(*) as count
      FROM library_items
      GROUP BY status
    `

    // Calculate some basic metrics
    const totalItems = parseInt(libraryItems[0]?.count) || 0
    const totalChapters = parseInt(chapters[0]?.count) || 0
    const totalVideos = parseInt(videos[0]?.count) || 0
    const totalUsers = parseInt(users[0]?.count) || 0

    // Transform content by type for usage data
    const usageData = contentByType.map((item: any) => ({
      name: item.type,
      views: parseInt(item.count) * 50, // Simulate views (50 views per item on average)
      growth: Math.random() * 30 - 5, // Random growth between -5% and 25%
      count: parseInt(item.count)
    }))

    // Transform top content
    const transformedTopContent = topContent.map((item: any, index: number) => ({
      title: item.title,
      type: item.type.toLowerCase(),
      views: Math.floor(Math.random() * 200) + 50, // Simulate views
      chapters: item.chapters,
      lastUpdated: item.updated_at
    }))

    // Calculate status breakdown
    const statusBreakdown = statusStats.reduce((acc: any, item: any) => {
      acc[item.status.toLowerCase()] = parseInt(item.count)
      return acc
    }, {})

    return NextResponse.json({
      success: true,
      analytics: {
        overview: {
          totalPageViews: totalItems * 75 + totalChapters * 25, // Simulate page views
          activeUsers: Math.floor(totalUsers * 0.6), // 60% of users are "active"
          avgSession: "12m 34s", // Static for now
          downloads: Math.floor(totalItems * 15), // Simulate downloads
        },
        content: {
          totalItems,
          totalChapters,
          totalVideos,
          totalUsers,
          usageData,
          topContent: transformedTopContent,
          statusBreakdown
        }
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 }
    )
  }
}
