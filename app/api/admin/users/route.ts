import { NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"

export async function GET() {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "Database not configured" }, { status: 500 })
    }

    const sql = neon(process.env.DATABASE_URL)
    
    // Get users ordered by creation date (most recent first)
    const users = await sql`
      SELECT 
        id,
        username,
        email,
        full_name as "fullName",
        role,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM users
      ORDER BY created_at DESC
      LIMIT 10
    `

    // Transform the data
    const transformedUsers = users.map((user: any) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      createdAt: user.createdAt ? new Date(user.createdAt).toISOString() : "",
      updatedAt: user.updatedAt ? new Date(user.updatedAt).toISOString() : "",
    }))

    return NextResponse.json(transformedUsers)
  } catch (error) {
    console.error("Error fetching admin users:", error)
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    )
  }
}
