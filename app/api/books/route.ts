import { type NextRequest, NextResponse } from "next/server"
import { getBooks } from "@/lib/data/books"
import { neon } from "@neondatabase/serverless"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const type = searchParams.get("type") || "all"
    const category = searchParams.get("category") || "all"
    const status = searchParams.get("status") || "all"
    const sortBy = searchParams.get("sortBy") || "title"
    const sortOrder = (searchParams.get("sortOrder") || "asc") as "asc" | "desc"

    const books = await getBooks(search, type, category, status, sortBy, sortOrder)

    return NextResponse.json(books)
  } catch (error) {
    console.error("Error fetching books:", error)
    return NextResponse.json(
      { error: "Failed to fetch books" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ success: false, error: "Database not configured" }, { status: 500 })
    }

    const body = await request.json()
    const { title, author, type, category, description, tags } = body

    // Validate required fields
    if (!title || !author || !type) {
      return NextResponse.json({ success: false, error: "Title, author, and type are required" }, { status: 400 })
    }

    // Use Neon client directly
    const sql = neon(process.env.DATABASE_URL)

    // Convert tags string to array if it's a string
    let tagsArray = []
    if (typeof tags === 'string') {
      tagsArray = tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0)
    } else if (Array.isArray(tags)) {
      tagsArray = tags
    }

    // Insert book into database
    const result = await sql`
      INSERT INTO library_items (title, author, type, category, description, tags, status, created_at, updated_at)
      VALUES (${title}, ${author}, ${type || 'Book'}, ${category || 'Uncategorized'}, ${description || ''}, ${tagsArray}, 'Draft', NOW(), NOW())
      RETURNING id, title, author, type, category, description, tags, status, created_at
    `

    const item = result[0]

    return NextResponse.json({
      success: true,
      book: {
        id: item.id,
        title: item.title,
        author: item.author,
        type: item.type,
        category: item.category,
        status: item.status,
        description: item.description,
        tags: Array.isArray(item.tags) ? item.tags : [],
        createdAt: item.created_at,
      },
    })
  } catch (error) {
    console.error("Error creating book:", error)
    return NextResponse.json({ success: false, error: "Failed to create book" }, { status: 500 })
  }
}
