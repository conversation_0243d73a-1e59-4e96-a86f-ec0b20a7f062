import { type NextRequest, NextResponse } from "next/server"
import { getChapters } from "@/lib/data/chapters"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const bookId = searchParams.get("bookId") || "all"
    const status = searchParams.get("status") || "all"

    const chapters = await getChapters(search, bookId, status)

    return NextResponse.json(chapters)
  } catch (error) {
    console.error("Error fetching chapters:", error)
    return NextResponse.json(
      { error: "Failed to fetch chapters" },
      { status: 500 }
    )
  }
}
