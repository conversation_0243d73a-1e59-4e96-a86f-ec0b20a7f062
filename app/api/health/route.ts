import { NextResponse } from "next/server"
import { testConnection, isDatabaseConfigured } from "@/lib/db"

export async function GET() {
  try {
    // Check if database is configured
    if (!isDatabaseConfigured()) {
      return NextResponse.json(
        {
          status: "error",
          message: "Database not configured",
          details: "DATABASE_URL environment variable is missing or invalid",
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      )
    }

    // Test the database connection
    const connectionTest = await testConnection()

    if (connectionTest.success) {
      return NextResponse.json({
        status: "healthy",
        message: "Database connection successful",
        database: "PostgreSQL (Neon)",
        timestamp: new Date().toISOString(),
        connectionTest: connectionTest.result,
      })
    } else {
      return NextResponse.json(
        {
          status: "error",
          message: "Database connection failed",
          error: connectionTest.error,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      )
    }
  } catch (error) {
    return NextResponse.json(
      {
        status: "error",
        message: "Health check failed",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
