import { type NextRequest, NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const q = searchParams.get("q") || ""

    if (q.length < 2) {
      return NextResponse.json({ results: [] })
    }

    const searchResults: any[] = []

    // Search books
    try {
      const booksResult = await query(
        `SELECT id, title, author, description, category, status, created_at 
         FROM books 
         WHERE title ILIKE $1 OR author ILIKE $1 OR description ILIKE $1 
         LIMIT 5`,
        [`%${q}%`],
      )

      booksResult.rows.forEach((book) => {
        searchResults.push({
          id: `book-${book.id}`,
          title: book.title,
          type: "book",
          author: book.author,
          description: book.description?.substring(0, 100) + "..." || "",
          date: book.created_at,
          status: book.status,
        })
      })
    } catch (error) {
      console.error("Error searching books:", error)
    }

    // Search chapters
    try {
      const chaptersResult = await query(
        `SELECT c.id, c.title, c.content, b.title as book_title 
         FROM chapters c 
         LEFT JOIN books b ON c.book_id = b.id 
         WHERE c.title ILIKE $1 OR c.content ILIKE $1 
         LIMIT 5`,
        [`%${q}%`],
      )

      chaptersResult.rows.forEach((chapter) => {
        searchResults.push({
          id: `chapter-${chapter.id}`,
          title: chapter.title,
          type: "chapter",
          book: chapter.book_title,
          description: chapter.content?.substring(0, 100) + "..." || "",
        })
      })
    } catch (error) {
      console.error("Error searching chapters:", error)
    }

    // Search videos (mock data for now)
    if (q.toLowerCase().includes("faith") || q.toLowerCase().includes("prayer")) {
      searchResults.push({
        id: "video-1",
        title: "Sunday Service - Faith",
        type: "video",
        minister: "Pastor John",
        duration: "45:32",
        description: "A powerful message about walking by faith...",
      })
    }

    // Search study guides (mock data for now)
    if (q.toLowerCase().includes("study") || q.toLowerCase().includes("guide")) {
      searchResults.push({
        id: "study-guide-1",
        title: "Prayer Study Guide",
        type: "study-guide",
        author: "Jane Smith",
        description: "A comprehensive guide to understanding prayer...",
      })
    }

    return NextResponse.json({ results: searchResults })
  } catch (error) {
    console.error("Search error:", error)
    return NextResponse.json({ results: [] }, { status: 500 })
  }
}
