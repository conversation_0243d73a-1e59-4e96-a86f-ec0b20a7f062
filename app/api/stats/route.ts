import { NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"

export async function GET() {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json(
        {
          success: false,
          message: "Database not configured",
        },
        { status: 500 },
      )
    }

    // Use Neon client directly for reliable counts
    const sql = neon(process.env.DATABASE_URL)
    
    // Get comprehensive stats
    const libraryCount = await sql`SELECT COUNT(*) as count FROM library_items`
    const chaptersCount = await sql`SELECT COUNT(*) as count FROM chapters`
    const usersCount = await sql`SELECT COUNT(*) as count FROM users`
    const subjectsCount = await sql`SELECT COUNT(*) as count FROM subjects`
    const videosCount = await sql`SELECT COUNT(*) as count FROM videos`
    
    // Get published vs draft counts
    const publishedCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE status = 'Published'`
    const draftCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE status = 'Draft'`
    
    // Get counts by type
    const bookCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE type = 'Book'`
    const studyGuideCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE type = 'Study Guide'`
    const transcriptCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE type = 'Transcript'`
    const courseCount = await sql`SELECT COUNT(*) as count FROM library_items WHERE type = 'Course'`

    return NextResponse.json({
      success: true,
      message: "Stats retrieved successfully",
      stats: {
        total: {
          libraryItems: parseInt(libraryCount[0]?.count) || 0,
          chapters: parseInt(chaptersCount[0]?.count) || 0,
          users: parseInt(usersCount[0]?.count) || 0,
          subjects: parseInt(subjectsCount[0]?.count) || 0,
          videos: parseInt(videosCount[0]?.count) || 0,
        },
        status: {
          published: parseInt(publishedCount[0]?.count) || 0,
          draft: parseInt(draftCount[0]?.count) || 0,
        },
        types: {
          books: parseInt(bookCount[0]?.count) || 0,
          studyGuides: parseInt(studyGuideCount[0]?.count) || 0,
          transcripts: parseInt(transcriptCount[0]?.count) || 0,
          courses: parseInt(courseCount[0]?.count) || 0,
        }
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Stats API error:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get stats",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
