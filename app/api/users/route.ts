import { type NextRequest, NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"
import bcrypt from "bcryptjs"

export async function POST(request: NextRequest) {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ success: false, error: "Database not configured" }, { status: 500 })
    }

    const body = await request.json()
    const { username, email, fullName, password, role } = body

    // Validate required fields
    if (!username || !email || !fullName || !password || !role) {
      return NextResponse.json({ success: false, error: "All fields are required" }, { status: 400 })
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Use Neon client directly
    const sql = neon(process.env.DATABASE_URL)

    // Insert user into database
    const result = await sql`
      INSERT INTO users (username, email, full_name, password_hash, role, created_at, updated_at)
      VALUES (${username}, ${email}, ${fullName}, ${hashedPassword}, ${role}, NOW(), NOW())
      RETURNING id, username, email, full_name, role, created_at
    `

    const user = result[0]

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        createdAt: user.created_at,
      },
    })
  } catch (error) {
    console.error("Error creating user:", error)
    return NextResponse.json({ success: false, error: "Failed to create user" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json([], { status: 500 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const role = searchParams.get("role") || "all"

    // Use Neon client directly
    const sql = neon(process.env.DATABASE_URL)

    // Get all users and filter in JavaScript for simplicity
    const result = await sql`
      SELECT id, username, email, full_name, role, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `

    let users = result.map((user: any) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      role: user.role,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
    }))

    // Apply filters
    if (search) {
      users = users.filter((user: any) =>
        user.fullName.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.username.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (role !== "all") {
      users = users.filter((user: any) => user.role === role)
    }

    return NextResponse.json(users)
  } catch (error) {
    console.error("Error fetching users:", error)
    return NextResponse.json([], { status: 500 })
  }
}
