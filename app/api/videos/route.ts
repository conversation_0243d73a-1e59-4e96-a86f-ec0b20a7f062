import { type NextRequest, NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"
import { isDatabaseConfigured } from "@/lib/database"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const minister = searchParams.get("minister") || "all"
    const sortBy = searchParams.get("sortBy") || "published_date"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    if (!isDatabaseConfigured()) {
      return NextResponse.json([], { status: 500 })
    }

    const sql = neon(process.env.DATABASE_URL!)

    // Get videos from database
    const videos = await sql`
      SELECT
        v.id,
        v.title,
        v.minister,
        v.duration,
        v.published_date,
        v.youtube_id,
        v.subjects,
        li.description,
        li.cover_image_url
      FROM videos v
      LEFT JOIN library_items li ON v.transcript_id = li.id
      ORDER BY v.published_date DESC
    `

    // Transform the data to match the expected format
    let transformedVideos = videos.map((video: any) => ({
      id: video.id,
      title: video.title || "Untitled Video",
      minister: video.minister || "Unknown Minister",
      duration: video.duration || "00:00",
      publishedDate: video.published_date ? new Date(video.published_date).toISOString().split('T')[0] : "",
      transcriptionStatus: "completed", // Assume completed if linked to transcript
      subjects: Array.isArray(video.subjects) ? video.subjects : [],
      thumbnail: video.cover_image_url || "/placeholder.svg?height=120&width=200",
      views: Math.floor(Math.random() * 2000) + 100, // Random views for demo
      description: video.description || "No description available",
      youtubeId: video.youtube_id || ""
    }))

    // Apply search filter
    if (search) {
      transformedVideos = transformedVideos.filter(
        (video) =>
          video.title.toLowerCase().includes(search.toLowerCase()) ||
          video.minister.toLowerCase().includes(search.toLowerCase()) ||
          video.description.toLowerCase().includes(search.toLowerCase()) ||
          video.subjects.some((subject: string) => subject.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // Apply minister filter
    if (minister !== "all") {
      transformedVideos = transformedVideos.filter((video) => video.minister === minister)
    }

    return NextResponse.json(transformedVideos)
  } catch (error) {
    console.error("Error fetching videos:", error)
    return NextResponse.json([], { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ success: false, error: "Database not configured" }, { status: 500 })
    }

    const body = await request.json()
    const { title, minister, youtube_id, description, subjects } = body

    // Validate required fields
    if (!title || !minister || !youtube_id) {
      return NextResponse.json({ success: false, error: "Title, minister, and YouTube ID are required" }, { status: 400 })
    }

    // Use Neon client directly
    const sql = neon(process.env.DATABASE_URL)

    // Insert video into database
    const result = await sql`
      INSERT INTO videos (title, minister, youtube_id, subjects, published_date, created_at, updated_at)
      VALUES (${title}, ${minister}, ${youtube_id}, ${subjects || []}, NOW(), NOW(), NOW())
      RETURNING id, title, minister, youtube_id, subjects, published_date, created_at
    `

    const video = result[0]

    // Also create a library item for the video transcript if needed
    const libraryResult = await sql`
      INSERT INTO library_items (title, author, type, category, description, status, created_at, updated_at)
      VALUES (${title + ' - Transcript'}, ${minister}, 'Transcript', 'Sermons', ${description || ''}, 'Draft', NOW(), NOW())
      RETURNING id
    `

    const libraryItem = libraryResult[0]

    // Link the video to the transcript
    await sql`
      UPDATE videos
      SET transcript_id = ${libraryItem.id}
      WHERE id = ${video.id}
    `

    return NextResponse.json({
      success: true,
      video: {
        id: video.id,
        title: video.title,
        minister: video.minister,
        youtubeId: video.youtube_id,
        subjects: video.subjects,
        publishedDate: video.published_date,
        createdAt: video.created_at,
        transcriptId: libraryItem.id,
      },
    })
  } catch (error) {
    console.error("Error creating video:", error)
    return NextResponse.json({ success: false, error: "Failed to create video" }, { status: 500 })
  }
}


