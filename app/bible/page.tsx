"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>eader } from "@/components/bible/bible-reader"
import { BibleNavigation } from "@/components/bible/bible-navigation"
import { BibleSearch } from "@/components/bible/bible-search"
import { Card } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { bibleAPI, type BibleChapter, BIBLE_BOOKS } from "@/lib/bible-api"

export default function BiblePage() {
  const [currentChapter, setCurrentChapter] = useState<BibleChapter | null>(null)
  const [selectedBook, setSelectedBook] = useState("psalms")
  const [selectedChapterNum, setSelectedChapterNum] = useState(23)
  const [loading, setLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])

  useEffect(() => {
    loadChapter(selectedBook, selectedChapterNum)
  }, [selectedB<PERSON>, selectedChapterNum])

  const loadChapter = async (book: string, chapter: number) => {
    setLoading(true)
    try {
      const chapterData = await bibleAPI.getChapter(book, chapter)
      setCurrentChapter(chapterData)
    } catch (error) {
      console.error("Error loading chapter:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleBookChange = (bookId: string) => {
    setSelectedBook(bookId)
    setSelectedChapterNum(1)
  }

  const handleChapterChange = (chapter: number) => {
    setSelectedChapterNum(chapter)
  }

  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([])
      return
    }

    try {
      const results = await bibleAPI.searchVerses(query)
      setSearchResults(results)
    } catch (error) {
      console.error("Error searching:", error)
    }
  }

  const handleVerseSelect = (reference: string) => {
    const parsed = bibleAPI.parseReference(reference)
    if (parsed) {
      const book = BIBLE_BOOKS.find(
        (b) => b.name.toLowerCase() === parsed.book.toLowerCase() || b.id.toLowerCase() === parsed.book.toLowerCase(),
      )
      if (book && parsed.chapter) {
        setSelectedBook(book.id)
        setSelectedChapterNum(parsed.chapter)
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Bible</h1>
          <p className="text-gray-600">Read and study the Word of God</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-1">
            <Card className="p-4">
              <Tabs defaultValue="navigate" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="navigate">Navigate</TabsTrigger>
                  <TabsTrigger value="search">Search</TabsTrigger>
                </TabsList>

                <TabsContent value="navigate" className="mt-4">
                  <BibleNavigation
                    selectedBook={selectedBook}
                    selectedChapter={selectedChapterNum}
                    onBookChange={handleBookChange}
                    onChapterChange={handleChapterChange}
                  />
                </TabsContent>

                <TabsContent value="search" className="mt-4">
                  <BibleSearch
                    onSearch={handleSearch}
                    searchResults={searchResults}
                    onVerseSelect={handleVerseSelect}
                  />
                </TabsContent>
              </Tabs>
            </Card>
          </div>

          {/* Bible Reader */}
          <div className="lg:col-span-3">
            <BibleReader chapter={currentChapter} loading={loading} onVerseSelect={handleVerseSelect} />
          </div>
        </div>
      </main>
    </div>
  )
}
