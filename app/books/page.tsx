"use client"

import { useState, useEffect } from "react"
import { TopNavigation } from "@/components/layout/top-navigation"
import { ViewToggle } from "@/components/ui/view-toggle"
import { BooksGridView } from "@/components/books/books-grid-view"
import { BooksListView } from "@/components/books/books-list-view"
import { AddBookModal } from "@/components/books/add-book-modal"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus } from "lucide-react"
// Using API endpoint instead of server action

interface Book {
  id: number
  title: string
  author: string
  type: string
  chapters: number
  status: string
  category: string
  lastUpdated: string
  tags: string[]
  cover: string
  description: string
}

export default function BooksPage() {
  const [view, setView] = useState<"grid" | "list">("grid")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortBy, setSortBy] = useState("title")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)

  const fetchBooks = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        type: selectedType,
        category: selectedCategory,
        status: selectedStatus,
        sortBy,
        sortOrder,
      })

      const response = await fetch(`/api/books?${params}`)
      if (response.ok) {
        const fetchedBooks = await response.json()
        setBooks(fetchedBooks)
      } else {
        console.error("Failed to fetch books")
        setBooks([])
      }
    } catch (error) {
      console.error("Error fetching books:", error)
      setBooks([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBooks()
  }, [searchTerm, selectedType, selectedCategory, selectedStatus, sortBy, sortOrder])

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Library</h1>
              <p className="text-gray-600">Manage your books, study guides, courses, and transcripts</p>
            </div>
            <div className="flex items-center space-x-2">
              <ViewToggle view={view} onViewChange={setView} />
              <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Book
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search library..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Book">Books</SelectItem>
                <SelectItem value="Study Guide">Study Guides</SelectItem>
                <SelectItem value="Course">Courses</SelectItem>
                <SelectItem value="Transcript">Transcripts</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Theology">Theology</SelectItem>
                <SelectItem value="Spiritual Life">Spiritual Life</SelectItem>
                <SelectItem value="Leadership">Leadership</SelectItem>
                <SelectItem value="Sermons">Sermons</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Published">Published</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : view === "grid" ? (
          <BooksGridView books={books} />
        ) : (
          <BooksListView
            books={books}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
          />
        )}
      </main>

      <AddBookModal open={showAddModal} onOpenChange={setShowAddModal} onBookAdded={fetchBooks} />
    </div>
  )
}
