"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Plus, Edit, BookOpen, Library, Quote } from "lucide-react"
import { getExpositoryTerms } from "@/app/actions/expository"

type ExpositoryTerm = {
  id: number
  term: string
  definition: string
  category: string
  scriptureReference: string
  source: string
  minister: string
  createdAt: string
  tags: string[]
}

export default function ExpositoryPage() {
  const [terms, setTerms] = useState<ExpositoryTerm[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [showAddModal, setShowAddModal] = useState(false)
  const [selectedLetter, setSelectedLetter] = useState("all")

  useEffect(() => {
    loadTerms()
  }, [])

  const loadTerms = async () => {
    try {
      const data = await getExpositoryTerms()
      setTerms(data)
    } catch (error) {
      console.error("Error loading expository terms:", error)
    } finally {
      setLoading(false)
    }
  }

  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")
  const categories = ["Theology", "Spiritual Life", "Biblical Terms", "Ministry", "Prophecy", "Worship"]

  const filteredTerms = terms.filter((term) => {
    const matchesSearch =
      term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
      term.definition.toLowerCase().includes(searchTerm.toLowerCase()) ||
      term.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = categoryFilter === "all" || term.category === categoryFilter
    const matchesLetter = selectedLetter === "all" || term.term.charAt(0).toUpperCase() === selectedLetter

    return matchesSearch && matchesCategory && matchesLetter
  })

  const groupedTerms = filteredTerms.reduce(
    (acc, term) => {
      const firstLetter = term.term.charAt(0).toUpperCase()
      if (!acc[firstLetter]) {
        acc[firstLetter] = []
      }
      acc[firstLetter].push(term)
      return acc
    },
    {} as Record<string, ExpositoryTerm[]>,
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Loading expository terms...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Expository</h1>
              <p className="text-gray-600">Biblical dictionary and definitions from ministry teachings</p>
            </div>
            <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
              <DialogTrigger asChild>
                <Button className="bg-red-600 hover:bg-red-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Term
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Expository Term</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="term">Term</Label>
                      <Input id="term" placeholder="Enter biblical term" />
                    </div>
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="definition">Definition</Label>
                    <Textarea id="definition" placeholder="Enter the biblical definition and explanation..." rows={6} />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="scripture">Scripture Reference</Label>
                      <Input id="scripture" placeholder="e.g., John 3:16" />
                    </div>
                    <div>
                      <Label htmlFor="minister">Minister/Source</Label>
                      <Input id="minister" placeholder="Enter minister name" />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="tags">Tags (comma separated)</Label>
                    <Input id="tags" placeholder="faith, salvation, grace" />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowAddModal(false)}>
                      Cancel
                    </Button>
                    <Button className="bg-red-600 hover:bg-red-700">Add Term</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{terms.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{categories.length}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Additions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-sm text-gray-500">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Most Referenced</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Faith</div>
                <p className="text-sm text-gray-500">45 references</p>
              </CardContent>
            </Card>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            <div className="lg:w-1/4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Search & Filter</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search terms..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Category</Label>
                    <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Alphabetical Index</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-4 gap-1">
                    <Button
                      size="sm"
                      variant={selectedLetter === "all" ? "default" : "outline"}
                      onClick={() => setSelectedLetter("all")}
                      className="text-xs"
                    >
                      All
                    </Button>
                    {alphabet.map((letter) => (
                      <Button
                        key={letter}
                        size="sm"
                        variant={selectedLetter === letter ? "default" : "outline"}
                        onClick={() => setSelectedLetter(letter)}
                        className="text-xs"
                      >
                        {letter}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="lg:w-3/4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Library className="w-5 h-5 mr-2" />
                    Expository Dictionary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {Object.keys(groupedTerms)
                      .sort()
                      .map((letter) => (
                        <div key={letter}>
                          <h3 className="text-xl font-bold text-gray-900 mb-4 border-b pb-2">{letter}</h3>
                          <div className="space-y-4">
                            {groupedTerms[letter].map((term) => (
                              <Card key={term.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="pt-6">
                                  <div className="flex items-start justify-between mb-3">
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-900 mb-1">{term.term}</h4>
                                      <Badge variant="outline" className="text-xs">
                                        {term.category}
                                      </Badge>
                                    </div>
                                    <Button size="sm" variant="outline">
                                      <Edit className="w-3 h-3" />
                                    </Button>
                                  </div>

                                  <p className="text-gray-700 mb-4 leading-relaxed">{term.definition}</p>

                                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                                    {term.scriptureReference && (
                                      <div className="flex items-center">
                                        <BookOpen className="w-3 h-3 mr-1" />
                                        {term.scriptureReference}
                                      </div>
                                    )}
                                    {term.minister && (
                                      <div className="flex items-center">
                                        <Quote className="w-3 h-3 mr-1" />
                                        {term.minister}
                                      </div>
                                    )}
                                  </div>

                                  {term.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-3">
                                      {term.tags.map((tag, index) => (
                                        <Badge key={index} variant="secondary" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
