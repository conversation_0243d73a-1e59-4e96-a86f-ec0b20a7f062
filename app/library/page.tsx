"use client"

import { useState, useEffect } from "react"
import { ViewToggle } from "@/components/ui/view-toggle"
import { LibraryGridView } from "@/components/library/library-grid-view"
import { LibraryListView } from "@/components/library/library-list-view"
import { AddContentModal } from "@/components/library/add-content-modal"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Plus, Book, GraduationCap, FileText } from "lucide-react"

// Define the LibraryItem type locally to avoid import issues
interface LibraryItem {
  id: string
  title: string
  type: string
  author?: string
  category?: string
  status: string
  created_at: string
  updated_at: string
  description?: string
  tags?: string[]
}

export default function LibraryPage() {
  const [view, setView] = useState<"grid" | "list">("grid")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState("all")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortBy, setSortBy] = useState("title")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [content, setContent] = useState<LibraryItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true)
      try {
        // Use fetch instead of server action to avoid SSR issues
        const response = await fetch(
          "/api/library?" +
            new URLSearchParams({
              search: searchTerm,
              type: activeTab === "all" ? selectedType : activeTab,
              category: selectedCategory,
              status: selectedStatus,
              sortBy,
              sortOrder,
            }),
        )

        if (response.ok) {
          const fetchedContent = await response.json()
          setContent(fetchedContent)
        } else {
          console.error("Failed to fetch content")
          setContent([])
        }
      } catch (error) {
        console.error("Error fetching content:", error)
        setContent([])
      } finally {
        setLoading(false)
      }
    }

    fetchContent()
  }, [searchTerm, selectedType, selectedCategory, selectedStatus, sortBy, sortOrder, activeTab])

  const getTabCounts = () => {
    return {
      all: content.length,
      book: content.filter((item) => item.type === "Book").length,
      "study-guide": content.filter((item) => item.type === "Study Guide").length,
      transcript: content.filter((item) => item.type === "Transcript").length,
    }
  }

  const counts = getTabCounts()

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <ViewToggle view={view} onViewChange={setView} />
              <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Content
              </Button>
            </div>
          </div>

          {/* Content Type Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <span>All Content</span>
                <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">{counts.all}</span>
              </TabsTrigger>
              <TabsTrigger value="book" className="flex items-center space-x-2">
                <Book className="w-4 h-4" />
                <span>Books</span>
                <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">{counts.book}</span>
              </TabsTrigger>
              <TabsTrigger value="study-guide" className="flex items-center space-x-2">
                <GraduationCap className="w-4 h-4" />
                <span>Study Guides</span>
                <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                  {counts["study-guide"]}
                </span>
              </TabsTrigger>
              <TabsTrigger value="transcript" className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>Transcripts</span>
                <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">{counts.transcript}</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search library..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {activeTab === "all" && (
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Book">Books</SelectItem>
                  <SelectItem value="Study Guide">Study Guides</SelectItem>
                  <SelectItem value="Transcript">Transcripts</SelectItem>
                  <SelectItem value="Course">Courses</SelectItem>
                </SelectContent>
              </Select>
            )}

            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Theology">Theology</SelectItem>
                <SelectItem value="Spiritual Life">Spiritual Life</SelectItem>
                <SelectItem value="Leadership">Leadership</SelectItem>
                <SelectItem value="Sermons">Sermons</SelectItem>
                <SelectItem value="Teaching">Teaching</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Published">Published</SelectItem>
                <SelectItem value="Draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : view === "grid" ? (
          <LibraryGridView content={content} />
        ) : (
          <LibraryListView
            content={content}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
          />
        )}
      </main>

      <AddContentModal open={showAddModal} onOpenChange={setShowAddModal} />
    </div>
  )
}
