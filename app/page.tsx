import { Suspense } from "react"
import { MasterSearch } from "@/components/search/master-search"
import { QuickStats } from "@/components/dashboard/quick-stats"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { MyTasks } from "@/components/dashboard/my-tasks"
import { DraftsInProgress } from "@/components/dashboard/drafts-in-progress"
import { AdminQuickAccess } from "@/components/dashboard/admin-quick-access"
import { AIAssistant } from "@/components/ai/ai-assistant"

export default async function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <main className="p-6 space-y-8">
        {/* Hero Section with Master Search */}
        <div className="flex flex-col items-center justify-center py-16 px-4">
          <div className="text-center mb-8">
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Welcome to <span className="text-amber-600">Chapters</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Search across all your biblical content, study guides, videos, and spiritual resources
            </p>
          </div>
          <MasterSearch />
        </div>

        {/* Dashboard Content */}
        <div className="max-w-7xl mx-auto">
          <Suspense fallback={<div className="animate-pulse bg-gray-200 h-32 rounded-lg"></div>}>
            <QuickStats />
          </Suspense>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
            <div className="lg:col-span-2 space-y-6">
              <Suspense fallback={<div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>}>
                <RecentActivity />
              </Suspense>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 rounded-lg"></div>}>
                  <MyTasks />
                </Suspense>

                <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 rounded-lg"></div>}>
                  <DraftsInProgress />
                </Suspense>
              </div>
            </div>

            <div className="space-y-6">
              <Suspense fallback={<div className="animate-pulse bg-gray-200 h-48 rounded-lg"></div>}>
                <AdminQuickAccess />
              </Suspense>

              <AIAssistant />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
