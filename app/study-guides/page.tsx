"use client"

import { useState } from "react"
import { TopNavigation } from "@/components/layout/top-navigation"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { GraduationCap, Search, Plus, Eye, Edit, Calendar, Users } from "lucide-react"

const studyGuides = [
  {
    id: 1,
    title: "New Believer's Foundation",
    description: "Essential teachings for new converts to establish their faith",
    chapters: 12,
    targetAudience: "New Believers",
    difficulty: "Beginner",
    estimatedTime: "6 weeks",
    author: "Pastor <PERSON>",
    lastUpdated: "2024-01-15",
    status: "Published",
    inFaithLibrary: true,
    tags: ["Foundation", "New Believers", "Basics"],
  },
  {
    id: 2,
    title: "Youth Leadership Training",
    description: "Developing young leaders in the church",
    chapters: 8,
    targetAudience: "Youth",
    difficulty: "Intermediate",
    estimatedTime: "4 weeks",
    author: "<PERSON> <PERSON>",
    lastUpdated: "2024-01-12",
    status: "Published",
    inFaithLibrary: false,
    tags: ["Leadership", "Youth", "Training"],
  },
  {
    id: 3,
    title: "Marriage and Family",
    description: "Biblical principles for strong marriages and families",
    chapters: 16,
    targetAudience: "Married Couples",
    difficulty: "Intermediate",
    estimatedTime: "8 weeks",
    author: "Elder Smith",
    lastUpdated: "2024-01-10",
    status: "Draft",
    inFaithLibrary: false,
    tags: ["Marriage", "Family", "Relationships"],
  },
  {
    id: 4,
    title: "Spiritual Warfare",
    description: "Understanding and engaging in spiritual battles",
    chapters: 10,
    targetAudience: "Mature Believers",
    difficulty: "Advanced",
    estimatedTime: "5 weeks",
    author: "Pastor Williams",
    lastUpdated: "2024-01-08",
    status: "Published",
    inFaithLibrary: true,
    tags: ["Spiritual Warfare", "Prayer", "Victory"],
  },
]

export default function StudyGuidesPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredGuides = studyGuides.filter(
    (guide) =>
      guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Study Guides</h1>
              <p className="text-gray-600">Structured learning materials for spiritual growth</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="w-4 h-4 mr-2" />
              New Study Guide
            </Button>
          </div>

          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search study guides..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGuides.map((guide) => (
            <Card key={guide.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <GraduationCap className="w-8 h-8 text-blue-600" />
                  <div className="flex space-x-1">
                    <Badge variant={guide.status === "Published" ? "default" : "secondary"}>{guide.status}</Badge>
                    {guide.inFaithLibrary && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        Faith Library
                      </Badge>
                    )}
                  </div>
                </div>
                <CardTitle className="text-lg">{guide.title}</CardTitle>
                <p className="text-sm text-gray-600 line-clamp-2">{guide.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Chapters:</span>
                      <p className="font-medium">{guide.chapters}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Duration:</span>
                      <p className="font-medium">{guide.estimatedTime}</p>
                    </div>
                  </div>

                  <div className="text-sm">
                    <span className="text-gray-500">Target Audience:</span>
                    <div className="flex items-center mt-1">
                      <Users className="w-4 h-4 mr-1 text-blue-600" />
                      <span className="font-medium">{guide.targetAudience}</span>
                    </div>
                  </div>

                  <div className="text-sm">
                    <span className="text-gray-500">Difficulty:</span>
                    <Badge
                      variant="outline"
                      className={`ml-2 ${
                        guide.difficulty === "Beginner"
                          ? "text-green-600 border-green-600"
                          : guide.difficulty === "Intermediate"
                            ? "text-yellow-600 border-yellow-600"
                            : "text-red-600 border-red-600"
                      }`}
                    >
                      {guide.difficulty}
                    </Badge>
                  </div>

                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    Updated {guide.lastUpdated}
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {guide.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      <Edit className="w-4 h-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  )
}
