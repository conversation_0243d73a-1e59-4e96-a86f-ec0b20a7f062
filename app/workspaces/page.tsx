"use client"

import { useState } from "react"
import { WorkspaceDashboard } from "@/components/workspace/workspace-dashboard"
import { WorkspaceView } from "@/components/workspace/workspace-view"

export default function WorkspacesPage() {
  const [selectedWorkspace, setSelectedWorkspace] = useState<string | null>(null)

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="h-[calc(100vh-4rem)]">
        {selectedWorkspace ? (
          <WorkspaceView workspaceId={selectedWorkspace} onBack={() => setSelectedWorkspace(null)} />
        ) : (
          <WorkspaceDashboard onSelectWorkspace={setSelectedWorkspace} />
        )}
      </main>
    </div>
  )
}
