"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, BookOpen, FileText, Video, Activity } from "lucide-react"
import { getAdminStats } from "@/app/actions/admin/users"

export function AdminStatsCards() {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalBooks: 0,
    totalChapters: 0,
    totalVideos: 0,
    activeUsers: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getAdminStats()
        // Ensure all values are numbers and provide defaults
        setStats({
          totalUsers: Number(data?.totalUsers) || 0,
          totalBooks: Number(data?.totalBooks) || 0,
          totalChapters: Number(data?.totalChapters) || 0,
          totalVideos: Number(data?.totalVideos) || 0,
          activeUsers: Number(data?.activeUsers) || 0,
        })
      } catch (error) {
        console.error("Error fetching stats:", error)
        // Set default values on error
        setStats({
          totalUsers: 0,
          totalBooks: 0,
          totalChapters: 0,
          totalVideos: 0,
          activeUsers: 0,
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      title: "Total Users",
      value: stats.totalUsers,
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Total Books",
      value: stats.totalBooks,
      icon: BookOpen,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Total Chapters",
      value: stats.totalChapters,
      icon: FileText,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Total Videos",
      value: stats.totalVideos,
      icon: Video,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      title: "Active Users",
      value: stats.activeUsers,
      icon: Activity,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon
        // Ensure the value is always a valid number or string
        const displayValue = typeof stat.value === "number" && !isNaN(stat.value) ? stat.value : 0

        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{displayValue}</div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
