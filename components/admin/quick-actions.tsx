import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Upload, Download, Settings, Users, Database } from "lucide-react"

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Plus className="w-5 h-5" />
            <span className="text-xs">Add User</span>
          </Button>

          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Upload className="w-5 h-5" />
            <span className="text-xs">Import Data</span>
          </Button>

          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Download className="w-5 h-5" />
            <span className="text-xs">Export Data</span>
          </Button>

          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Database className="w-5 h-5" />
            <span className="text-xs">Backup DB</span>
          </Button>

          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Users className="w-5 h-5" />
            <span className="text-xs">Manage Users</span>
          </Button>

          <Button variant="outline" className="h-20 flex-col space-y-2">
            <Settings className="w-5 h-5" />
            <span className="text-xs">Settings</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
