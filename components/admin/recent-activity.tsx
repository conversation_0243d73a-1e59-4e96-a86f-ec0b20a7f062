"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Activity, User, BookOpen, Video, FileText } from "lucide-react"
import { useEffect, useState } from "react"

interface ActivityItem {
  id: string
  type: string
  description: string
  timestamp: string
  icon: any
  color: string
}

export function RecentActivity() {
  const [activities, setActivities] = useState<ActivityItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        // Fetch recent data from multiple sources to create activity feed
        const [usersResponse, booksResponse, chaptersResponse, videosResponse] = await Promise.all([
          fetch('/api/admin/users'),
          fetch('/api/books'),
          fetch('/api/chapters'),
          fetch('/api/videos')
        ])

        const users = usersResponse.ok ? await usersResponse.json() : []
        const books = booksResponse.ok ? await booksResponse.json() : []
        const chapters = chaptersResponse.ok ? await chaptersResponse.json() : []
        const videos = videosResponse.ok ? await videosResponse.json() : []

        // Create activity items from recent data
        const recentActivities: ActivityItem[] = []

        // Add recent users (last 3)
        users.slice(0, 2).forEach((user: any, index: number) => {
          recentActivities.push({
            id: `user-${user.id}`,
            type: "user_created",
            description: `New user registered: ${user.fullName || user.username}`,
            timestamp: `${index + 1} day${index > 0 ? 's' : ''} ago`,
            icon: User,
            color: "text-blue-600"
          })
        })

        // Add recent books (last 2)
        books.slice(0, 2).forEach((book: any, index: number) => {
          recentActivities.push({
            id: `book-${book.id}`,
            type: book.status === 'Published' ? "book_published" : "book_updated",
            description: `Book '${book.title}' was ${book.status === 'Published' ? 'published' : 'updated'}`,
            timestamp: `${index + 2} hours ago`,
            icon: BookOpen,
            color: book.status === 'Published' ? "text-green-600" : "text-orange-600"
          })
        })

        // Add recent chapters (last 2)
        chapters.slice(0, 2).forEach((chapter: any, index: number) => {
          recentActivities.push({
            id: `chapter-${chapter.id}`,
            type: "chapter_updated",
            description: `Chapter '${chapter.title}' was updated`,
            timestamp: `${index + 4} hours ago`,
            icon: FileText,
            color: "text-purple-600"
          })
        })

        // Add recent videos (last 1)
        videos.slice(0, 1).forEach((video: any, index: number) => {
          recentActivities.push({
            id: `video-${video.id}`,
            type: "video_uploaded",
            description: `Video '${video.title}' was uploaded`,
            timestamp: `${index + 6} hours ago`,
            icon: Video,
            color: "text-indigo-600"
          })
        })

        // Sort by most recent and take top 6
        setActivities(recentActivities.slice(0, 6))
      } catch (error) {
        console.error("Error fetching recent activity:", error)
        setActivities([])
      } finally {
        setLoading(false)
      }
    }

    fetchRecentActivity()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse flex items-start space-x-3 p-3">
                <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.length > 0 ? (
          activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
              <div className="p-2 bg-gray-100 rounded-lg">
                <activity.icon className={`w-4 h-4 ${activity.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-900">{activity.description}</p>
                <p className="text-xs text-gray-500 mt-1">{activity.timestamp}</p>
              </div>
              <Badge variant="outline" className="text-xs">
                {activity.type.replace("_", " ")}
              </Badge>
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            <Activity className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No recent activity</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
