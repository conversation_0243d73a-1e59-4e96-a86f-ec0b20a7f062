"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Server, Database, Cpu, HardDrive } from "lucide-react"
import { useEffect, useState } from "react"

export function SystemHealth() {
  const [dbStatus, setDbStatus] = useState<'healthy' | 'error' | 'checking'>('checking')
  const [apiStatus, setApiStatus] = useState<'online' | 'offline' | 'checking'>('checking')

  useEffect(() => {
    const checkSystemHealth = async () => {
      // Check database health
      try {
        const dbResponse = await fetch('/api/test-db')
        setDbStatus(dbResponse.ok ? 'healthy' : 'error')
      } catch (error) {
        setDbStatus('error')
      }

      // Check API health
      try {
        const apiResponse = await fetch('/api/stats')
        setApiStatus(apiResponse.ok ? 'online' : 'offline')
      } catch (error) {
        setApiStatus('offline')
      }
    }

    checkSystemHealth()
    // Check every 30 seconds
    const interval = setInterval(checkSystemHealth, 30000)
    return () => clearInterval(interval)
  }, [])

  const getDbStatusBadge = () => {
    switch (dbStatus) {
      case 'healthy':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Healthy</Badge>
      case 'error':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Error</Badge>
      default:
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Checking...</Badge>
    }
  }

  const getApiStatusBadge = () => {
    switch (apiStatus) {
      case 'online':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Online</Badge>
      case 'offline':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Offline</Badge>
      default:
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Checking...</Badge>
    }
  }
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Server className="w-5 h-5 mr-2" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Database className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Database</span>
            </div>
            {getDbStatusBadge()}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Cpu className="w-4 h-4 text-orange-600" />
                <span className="text-sm">CPU Usage</span>
              </div>
              <span className="text-sm text-gray-600">23%</span>
            </div>
            <Progress value={23} className="h-2" />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <HardDrive className="w-4 h-4 text-purple-600" />
                <span className="text-sm">Storage</span>
              </div>
              <span className="text-sm text-gray-600">2.4 GB / 100 GB</span>
            </div>
            <Progress value={2.4} className="h-2" />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">API Status</span>
            {getApiStatusBadge()}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm">Backup Status</span>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Last: 2 hours ago
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
