"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { BIBLE_BOOKS } from "@/lib/bible-api"

interface BibleNavigationProps {
  selectedBook: string
  selectedChapter: number
  onBookChange: (bookId: string) => void
  onChapterChange: (chapter: number) => void
}

export function BibleNavigation({
  selectedBook,
  selectedChapter,
  onBookChange,
  onChapterChange,
}: BibleNavigationProps) {
  const [testament, setTestament] = useState<"old" | "new" | "all">("all")

  const currentBook = BIBLE_BOOKS.find((book) => book.id === selectedBook)

  const filteredBooks = BIBLE_BOOKS.filter((book) => testament === "all" || book.testament === testament)

  const handlePreviousChapter = () => {
    if (selectedChapter > 1) {
      onChapterChange(selectedChapter - 1)
    } else {
      // Go to previous book's last chapter
      const currentIndex = BIBLE_BOOKS.findIndex((book) => book.id === selectedBook)
      if (currentIndex > 0) {
        const previousBook = BIBLE_BOOKS[currentIndex - 1]
        onBookChange(previousBook.id)
        onChapterChange(previousBook.chapters)
      }
    }
  }

  const handleNextChapter = () => {
    if (currentBook && selectedChapter < currentBook.chapters) {
      onChapterChange(selectedChapter + 1)
    } else {
      // Go to next book's first chapter
      const currentIndex = BIBLE_BOOKS.findIndex((book) => book.id === selectedBook)
      if (currentIndex < BIBLE_BOOKS.length - 1) {
        const nextBook = BIBLE_BOOKS[currentIndex + 1]
        onBookChange(nextBook.id)
        onChapterChange(1)
      }
    }
  }

  return (
    <div className="space-y-4">
      {/* Testament Filter */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Testament</label>
        <Select value={testament} onValueChange={(value: "old" | "new" | "all") => setTestament(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="old">Old Testament</SelectItem>
            <SelectItem value="new">New Testament</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Book Selection */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Book</label>
        <Select value={selectedBook} onValueChange={onBookChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {filteredBooks.map((book) => (
              <SelectItem key={book.id} value={book.id}>
                <div className="flex items-center justify-between w-full">
                  <span>{book.name}</span>
                  <Badge variant="outline" className="ml-2">
                    {book.chapters}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Chapter Selection */}
      {currentBook && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Chapter</label>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePreviousChapter}
              disabled={selectedBook === "genesis" && selectedChapter === 1}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            <Select
              value={selectedChapter.toString()}
              onValueChange={(value) => onChapterChange(Number.parseInt(value))}
            >
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <ScrollArea className="h-48">
                  {Array.from({ length: currentBook.chapters }, (_, i) => i + 1).map((chapter) => (
                    <SelectItem key={chapter} value={chapter.toString()}>
                      Chapter {chapter}
                    </SelectItem>
                  ))}
                </ScrollArea>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="icon"
              onClick={handleNextChapter}
              disabled={selectedBook === "revelation" && selectedChapter === 22}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Quick Chapter Grid */}
      {currentBook && currentBook.chapters <= 31 && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Quick Select</label>
          <div className="grid grid-cols-5 gap-1">
            {Array.from({ length: currentBook.chapters }, (_, i) => i + 1).map((chapter) => (
              <Button
                key={chapter}
                variant={chapter === selectedChapter ? "default" : "outline"}
                size="sm"
                onClick={() => onChapterChange(chapter)}
                className="text-xs"
              >
                {chapter}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
