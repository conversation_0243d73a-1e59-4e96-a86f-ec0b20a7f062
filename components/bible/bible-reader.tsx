"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { BookOpen } from "lucide-react"
import type { BibleChapter } from "@/lib/bible-api"
import { KJVVerse } from "./kjv-verse"

interface BibleReaderProps {
  chapter: BibleChapter | null
  loading: boolean
  onVerseSelect: (reference: string) => void
}

export function BibleReader({ chapter, loading, onVerseSelect }: BibleReaderProps) {
  const [selectedVerse, setSelectedVerse] = useState<number | null>(null)
  const [fontSize, setFontSize] = useState(16)

  const handleVerseClick = (verseNumber: number) => {
    setSelectedVerse(verseNumber === selectedVerse ? null : verseNumber)
  }

  const copyVerse = (verse: any) => {
    const text = `"${verse.text}" - ${verse.book_name} ${verse.chapter}:${verse.verse}`
    navigator.clipboard.writeText(text)
  }

  const shareVerse = (verse: any) => {
    const text = `"${verse.text}" - ${verse.book_name} ${verse.chapter}:${verse.verse}`
    if (navigator.share) {
      navigator.share({
        title: `${verse.book_name} ${verse.chapter}:${verse.verse}`,
        text: text,
      })
    } else {
      copyVerse(verse)
    }
  }

  if (loading) {
    return (
      <Card className="h-96">
        <CardContent className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    )
  }

  if (!chapter) {
    return (
      <Card className="h-96">
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Select a book and chapter to begin reading</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl">{chapter.reference}</CardTitle>
            {chapter.translation_name && (
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {chapter.translation_name}
                </Badge>
                <span className="text-sm text-gray-500">KJV</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setFontSize(Math.max(12, fontSize - 2))}>
              A-
            </Button>
            <span className="text-sm text-gray-500">{fontSize}px</span>
            <Button variant="outline" size="sm" onClick={() => setFontSize(Math.min(24, fontSize + 2))}>
              A+
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-2">
          {chapter.verses && chapter.verses.length > 0 ? (
            chapter.verses.map((verse) => (
              <KJVVerse
                key={verse.verse}
                verse={verse}
                isSelected={selectedVerse === verse.verse}
                onSelect={() => handleVerseClick(verse.verse)}
                fontSize={fontSize}
              />
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No verses available for this chapter</p>
            </div>
          )}
        </div>

        {chapter.translation_note && (
          <>
            <Separator className="my-6" />
            <div className="text-sm text-gray-600 bg-amber-50 p-4 rounded-lg border border-amber-200">
              <p className="font-medium mb-2 text-amber-800">About the King James Version:</p>
              <p className="text-amber-700">{chapter.translation_note}</p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
