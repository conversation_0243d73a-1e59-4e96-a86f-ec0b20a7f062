"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, BookOpen } from "lucide-react"
import type { BibleVerse } from "@/lib/bible-api"

interface BibleSearchProps {
  onSearch: (query: string) => void
  searchResults: BibleVerse[]
  onVerseSelect: (reference: string) => void
}

export function BibleSearch({ onSearch, searchResults, onVerseSelect }: BibleSearchProps) {
  const [query, setQuery] = useState("")
  const [loading, setLoading] = useState(false)

  const handleSearch = async () => {
    if (!query.trim()) return

    setLoading(true)
    try {
      await onSearch(query)
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  const handleVerseClick = (verse: BibleVerse) => {
    const reference = `${verse.book_name} ${verse.chapter}:${verse.verse}`
    onVerseSelect(reference)
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Search the Bible</label>
        <div className="flex space-x-2">
          <Input
            placeholder="Enter keywords or reference..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Button onClick={handleSearch} disabled={loading || !query.trim()}>
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Search className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Quick Reference Input */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Go to Reference</label>
        <Input
          placeholder="e.g., John 3:16, Romans 8, Psalm 23"
          onKeyPress={(e) => {
            if (e.key === "Enter") {
              const target = e.target as HTMLInputElement
              onVerseSelect(target.value)
              target.value = ""
            }
          }}
        />
        <p className="text-xs text-gray-500">Press Enter to navigate to a specific verse or chapter</p>
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Search Results</label>
          <ScrollArea className="h-64 border rounded-md p-2">
            <div className="space-y-3">
              {searchResults.map((verse, index) => (
                <div
                  key={index}
                  className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => handleVerseClick(verse)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="text-xs">
                      {verse.book_name} {verse.chapter}:{verse.verse}
                    </Badge>
                    <BookOpen className="w-3 h-3 text-gray-400" />
                  </div>
                  <p className="text-sm text-gray-700 line-clamp-3">{verse.text}</p>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Popular Verses */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Popular KJV Verses</label>
        <div className="grid grid-cols-1 gap-2">
          {[
            "John 3:16",
            "Romans 8:28",
            "Philippians 4:13",
            "Jeremiah 29:11",
            "Psalm 23:1",
            "Isaiah 41:10",
            "Romans 10:9",
            "John 14:6",
            "Psalm 119:105",
            "2 Timothy 3:16",
            "1 John 1:9",
            "Matthew 5:16",
          ].map((reference) => (
            <Button
              key={reference}
              variant="outline"
              size="sm"
              onClick={() => onVerseSelect(reference)}
              className="justify-start text-xs"
            >
              {reference}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}
