"use client"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Copy, Share, Bookmark, Highlighter } from "lucide-react"
import type { BibleVerse } from "@/lib/bible-api"

interface KJVVerseProps {
  verse: BibleVerse
  isSelected: boolean
  onSelect: () => void
  fontSize: number
}

export function KJVVerse({ verse, isSelected, onSelect, fontSize }: KJVVerseProps) {
  const copyVerse = () => {
    const text = `"${verse.text}" - ${verse.book_name} ${verse.chapter}:${verse.verse} (KJV)`
    navigator.clipboard.writeText(text)
  }

  const shareVerse = () => {
    const text = `"${verse.text}" - ${verse.book_name} ${verse.chapter}:${verse.verse} (KJV)`
    if (navigator.share) {
      navigator.share({
        title: `${verse.book_name} ${verse.chapter}:${verse.verse} (KJ<PERSON>)`,
        text: text,
      })
    } else {
      copyVerse()
    }
  }

  return (
    <div className="group">
      <div
        className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
          isSelected ? "bg-blue-50 border border-blue-200" : "hover:bg-gray-50"
        }`}
        onClick={onSelect}
      >
        <Badge
          variant="outline"
          className="mt-1 text-xs font-mono min-w-[2.5rem] justify-center bg-amber-50 text-amber-700 border-amber-200"
        >
          {verse.verse}
        </Badge>

        <p
          className="flex-1 leading-relaxed text-gray-900 font-serif"
          style={{ fontSize: `${fontSize}px`, lineHeight: "1.7" }}
        >
          {verse.text}
        </p>
      </div>

      {isSelected && (
        <div className="mt-3 ml-14 flex items-center space-x-2 pb-2">
          <Button variant="outline" size="sm" onClick={copyVerse}>
            <Copy className="w-3 h-3 mr-1" />
            Copy KJV
          </Button>

          <Button variant="outline" size="sm" onClick={shareVerse}>
            <Share className="w-3 h-3 mr-1" />
            Share
          </Button>

          <Button variant="outline" size="sm">
            <Bookmark className="w-3 h-3 mr-1" />
            Bookmark
          </Button>

          <Button variant="outline" size="sm">
            <Highlighter className="w-3 h-3 mr-1" />
            Highlight
          </Button>
        </div>
      )}
    </div>
  )
}
