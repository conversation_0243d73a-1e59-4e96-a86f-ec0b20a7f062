"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, Calendar } from "lucide-react"

interface Book {
  id: number
  title: string
  author: string
  type: string
  chapters: number
  status: string
  category: string
  lastUpdated: string
  tags: string[]
  cover: string
  description: string
}

interface BooksGridViewProps {
  books: Book[]
}

export function BooksGridView({ books }: BooksGridViewProps) {
  return (
    <div className="space-y-8">
      {/* Bookshelf Display */}
      <div className="bg-gradient-to-b from-amber-100 to-amber-200 rounded-lg p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
          {books.map((book) => (
            <div key={book.id} className="group cursor-pointer">
              <div className="relative">
                <div className="bg-gradient-to-b from-blue-600 to-blue-800 rounded-sm shadow-lg transform group-hover:scale-105 transition-transform duration-200 h-32 w-24 mx-auto">
                  <div className="p-2 text-white text-xs">
                    <div className="font-bold line-clamp-3 mb-1">{book.title}</div>
                    <div className="text-blue-200 text-xs">{book.author}</div>
                  </div>
                  <div className="absolute bottom-1 right-1">
                    <Badge variant="secondary" className="text-xs">
                      {book.type}
                    </Badge>
                  </div>
                </div>
                <div className="text-center mt-2">
                  <div className="text-xs font-medium truncate">{book.title}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Card View for Additional Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {books.map((book) => (
          <Card key={book.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <Badge variant={book.status === "Published" ? "default" : "secondary"}>{book.status}</Badge>
                <Badge variant="outline">{book.type}</Badge>
              </div>

              <h3 className="font-semibold text-lg mb-1 line-clamp-2">{book.title}</h3>
              <p className="text-sm text-gray-600 mb-2">by {book.author}</p>
              <p className="text-xs text-gray-500 mb-3 line-clamp-2">{book.description}</p>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Chapters:</span>
                  <span className="font-medium">{book.chapters}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Category:</span>
                  <span className="font-medium">{book.category}</span>
                </div>
                <div className="flex items-center text-gray-500">
                  <Calendar className="w-3 h-3 mr-1" />
                  <span className="text-xs">{book.lastUpdated}</span>
                </div>
              </div>

              <div className="flex flex-wrap gap-1 mt-3 mb-3">
                {book.tags.slice(0, 2).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {book.tags.length > 2 && (
                  <Badge variant="outline" className="text-xs">
                    +{book.tags.length - 2}
                  </Badge>
                )}
              </div>

              <div className="flex space-x-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
