"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react"

interface Book {
  id: number
  title: string
  author: string
  type: string
  chapters: number
  status: string
  category: string
  lastUpdated: string
  tags: string[]
  cover: string
  description: string
}

interface BooksListViewProps {
  books: Book[]
  sortBy: string
  setSortBy: (field: string) => void
  sortOrder: "asc" | "desc"
  setSortOrder: (order: "asc" | "desc") => void
}

export function BooksListView({ books, sortBy, setSortBy, sortOrder, setSortOrder }: BooksListViewProps) {
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("asc")
    }
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return <ArrowUpDown className="w-4 h-4" />
    return sortOrder === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />
  }

  return (
    <div className="bg-white rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">Cover</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("title")} className="h-auto p-0 font-semibold">
                Title <SortIcon field="title" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("author")} className="h-auto p-0 font-semibold">
                Author <SortIcon field="author" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("type")} className="h-auto p-0 font-semibold">
                Type <SortIcon field="type" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("chapters")} className="h-auto p-0 font-semibold">
                Chapters <SortIcon field="chapters" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("category")} className="h-auto p-0 font-semibold">
                Category <SortIcon field="category" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("status")} className="h-auto p-0 font-semibold">
                Status <SortIcon field="status" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("lastUpdated")} className="h-auto p-0 font-semibold">
                Updated <SortIcon field="lastUpdated" />
              </Button>
            </TableHead>
            <TableHead>Tags</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {books.map((book) => (
            <TableRow key={book.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="w-8 h-10 bg-gradient-to-b from-blue-600 to-blue-800 rounded-sm flex items-center justify-center">
                  <div className="text-white text-xs font-bold text-center px-1">
                    {book.title
                      .split(" ")
                      .map((word) => word[0])
                      .join("")
                      .slice(0, 2)}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{book.title}</div>
                  <div className="text-sm text-gray-500 line-clamp-1">{book.description}</div>
                </div>
              </TableCell>
              <TableCell>{book.author}</TableCell>
              <TableCell>
                <Badge variant="outline">{book.type}</Badge>
              </TableCell>
              <TableCell>{book.chapters}</TableCell>
              <TableCell>{book.category}</TableCell>
              <TableCell>
                <Badge variant={book.status === "Published" ? "default" : "secondary"}>{book.status}</Badge>
              </TableCell>
              <TableCell className="text-sm text-gray-500">{book.lastUpdated}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {book.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {book.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{book.tags.length - 2}
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex space-x-1">
                  <Button size="sm" variant="outline">
                    <Eye className="w-3 h-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="w-3 h-3" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
