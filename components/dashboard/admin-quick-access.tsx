"use client"

import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Shield, Users, Upload, BarChart3, Database } from "lucide-react"

const adminActions = [
  {
    title: "User Management",
    description: "Manage users and permissions",
    href: "/admin/users",
    icon: Users,
    color: "text-blue-600",
  },
  {
    title: "Import Content",
    description: "Bulk import books and chapters",
    href: "/admin/import",
    icon: Upload,
    color: "text-green-600",
  },
  {
    title: "Analytics",
    description: "View usage statistics",
    href: "/admin/analytics",
    icon: BarChart3,
    color: "text-purple-600",
  },
  {
    title: "Database",
    description: "Database management tools",
    href: "/admin/database",
    icon: Database,
    color: "text-orange-600",
  },
]

export function AdminQuickAccess() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="w-5 h-5 text-red-600 mr-2" />
          Admin Quick Access
        </CardTitle>
        <CardDescription>Administrative tools and management functions</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {adminActions.map((action) => (
            <Link key={action.href} href={action.href}>
              <Button variant="ghost" className="h-auto p-3 flex flex-col items-start space-y-1 hover:bg-gray-50">
                <div className="flex items-center space-x-2">
                  <action.icon className={`w-4 h-4 ${action.color}`} />
                  <span className="font-medium text-sm">{action.title}</span>
                </div>
                <span className="text-xs text-gray-500 text-left">{action.description}</span>
              </Button>
            </Link>
          ))}
        </div>

        <div className="mt-4 pt-4 border-t">
          <Link href="/admin">
            <Button className="w-full" variant="outline">
              <Shield className="w-4 h-4 mr-2" />
              Open Full Admin Panel
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
