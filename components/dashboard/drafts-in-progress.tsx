"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, FileText, Clock } from "lucide-react"
import { useEffect, useState } from "react"

interface Draft {
  id: string
  title: string
  type: string
  lastEdited: string
  progress: number
}

export function DraftsInProgress() {
  const [drafts, setDrafts] = useState<Draft[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDrafts = async () => {
      try {
        // Fetch books and filter for drafts
        const response = await fetch('/api/books')
        if (response.ok) {
          const books = await response.json()

          // Filter for draft items and create draft objects
          const draftItems = books
            .filter((book: any) => book.status === 'Draft')
            .slice(0, 3)
            .map((book: any, index: number) => ({
              id: book.id.toString(),
              title: book.title,
              type: book.type,
              lastEdited: `${index + 1} day${index > 0 ? 's' : ''} ago`,
              progress: Math.floor(Math.random() * 80) + 20 // Random progress for demo
            }))

          setDrafts(draftItems)
        }
      } catch (error) {
        console.error("Error fetching drafts:", error)
        setDrafts([])
      } finally {
        setLoading(false)
      }
    }

    fetchDrafts()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Drafts in Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse p-3 rounded-lg border">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-2 bg-gray-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="w-5 h-5 mr-2" />
          Drafts in Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {drafts.length > 0 ? (
          drafts.map((draft) => (
            <div key={draft.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50">
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900">{draft.title}</h4>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-gray-500">{draft.type}</span>
                  <Clock className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-500">{draft.lastEdited}</span>
                </div>
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                      <div className="bg-indigo-600 h-1.5 rounded-full" style={{ width: `${draft.progress}%` }}></div>
                    </div>
                    <span className="text-xs text-gray-500">{draft.progress}%</span>
                  </div>
                </div>
              </div>
              <Button size="sm" variant="outline">
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            <FileText className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No drafts in progress</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
