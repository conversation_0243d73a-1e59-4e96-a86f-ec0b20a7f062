import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { CheckCircle, Circle, Clock } from "lucide-react"

const tasks = [
  {
    id: 1,
    title: "Review Chapter 15 - Holiness",
    status: "pending",
    priority: "high",
    dueDate: "Today",
  },
  {
    id: 2,
    title: "Transcribe Sunday Service",
    status: "in-progress",
    priority: "medium",
    dueDate: "Tomorrow",
  },
  {
    id: 3,
    title: "Update Scripture References",
    status: "completed",
    priority: "low",
    dueDate: "Yesterday",
  },
]

export function MyTasks() {
  const completedTasks = tasks.filter((task) => task.status === "completed").length
  const totalTasks = tasks.length
  const progressPercentage = (completedTasks / totalTasks) * 100

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>My Tasks</span>
          <span className="text-sm font-normal text-gray-500">
            {completedTasks}/{totalTasks} completed
          </span>
        </CardTitle>
        <Progress value={progressPercentage} className="w-full" />
      </CardHeader>
      <CardContent className="space-y-3">
        {tasks.map((task) => (
          <div key={task.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
            {task.status === "completed" ? (
              <CheckCircle className="w-5 h-5 text-green-500" />
            ) : (
              <Circle className="w-5 h-5 text-gray-400" />
            )}
            <div className="flex-1">
              <p className={`text-sm ${task.status === "completed" ? "line-through text-gray-500" : "text-gray-900"}`}>
                {task.title}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500">{task.dueDate}</span>
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    task.priority === "high"
                      ? "bg-red-100 text-red-800"
                      : task.priority === "medium"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-green-100 text-green-800"
                  }`}
                >
                  {task.priority}
                </span>
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
