"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { BookOpen, FileText, Video, Tags } from "lucide-react"
import { useEffect, useState } from "react"

interface Stats {
  libraryItems: number
  chapters: number
  videos: number
  subjects: number
}

export function QuickStats() {
  const [stats, setStats] = useState<Stats>({
    libraryItems: 0,
    chapters: 0,
    videos: 0,
    subjects: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch real stats from the dedicated stats API
        const response = await fetch('/api/stats')
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.stats) {
            setStats({
              libraryItems: data.stats.total.libraryItems || 0,
              chapters: data.stats.total.chapters || 0,
              videos: data.stats.total.videos || 0,
              subjects: data.stats.total.subjects || 0
            })
          }
        }
      } catch (error) {
        console.error("Error fetching stats:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statItems = [
    {
      title: "Library Items",
      value: loading ? "..." : stats.libraryItems.toString(),
      change: "Books, Guides, Courses",
      icon: BookOpen,
      color: "text-blue-600",
    },
    {
      title: "Chapters",
      value: loading ? "..." : stats.chapters.toString(),
      change: "Published content",
      icon: FileText,
      color: "text-green-600",
    },
    {
      title: "Videos",
      value: loading ? "..." : stats.videos.toString(),
      change: "Sermons & teachings",
      icon: Video,
      color: "text-purple-600",
    },
    {
      title: "Subjects",
      value: loading ? "..." : stats.subjects.toString(),
      change: "Topic categories",
      icon: Tags,
      color: "text-orange-600",
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((stat) => (
        <Card key={stat.title} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
            <stat.icon className={`w-4 h-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
