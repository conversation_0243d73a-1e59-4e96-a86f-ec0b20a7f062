"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, User, BookOpen, Video, FileText } from "lucide-react"
import { useEffect, useState } from "react"

interface Activity {
  id: string
  type: string
  title: string
  user: string
  action: string
  time: string
  icon: any
}

export function RecentActivity() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        // Fetch recent library items and chapters to simulate activity
        const [booksResponse, chaptersResponse] = await Promise.all([
          fetch('/api/books'),
          fetch('/api/chapters')
        ])

        const books = booksResponse.ok ? await booksResponse.json() : []
        const chapters = chaptersResponse.ok ? await chaptersResponse.json() : []

        // Create activity items from recent data
        const recentActivities: Activity[] = []

        // Add recent books
        books.slice(0, 2).forEach((book: any, index: number) => {
          recentActivities.push({
            id: `book-${book.id}`,
            type: book.type.toLowerCase(),
            title: book.title,
            user: book.author,
            action: book.status === 'Published' ? 'published' : 'updated',
            time: `${index + 1} day${index > 0 ? 's' : ''} ago`,
            icon: book.type === 'Book' ? BookOpen : FileText
          })
        })

        // Add recent chapters
        chapters.slice(0, 2).forEach((chapter: any, index: number) => {
          recentActivities.push({
            id: `chapter-${chapter.id}`,
            type: 'chapter',
            title: chapter.title,
            user: chapter.author,
            action: chapter.status === 'Published' ? 'published' : 'updated',
            time: `${index + 2} hours ago`,
            icon: FileText
          })
        })

        setActivities(recentActivities.slice(0, 4))
      } catch (error) {
        console.error("Error fetching recent activity:", error)
        // Fallback to empty array
        setActivities([])
      } finally {
        setLoading(false)
      }
    }

    fetchRecentActivity()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.length > 0 ? (
          activities.map((activity) => (
            <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
              <activity.icon className="w-5 h-5 text-gray-400" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <User className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-500">{activity.user}</span>
                  <Badge variant="secondary" className="text-xs">
                    {activity.action}
                  </Badge>
                </div>
              </div>
              <span className="text-xs text-gray-400">{activity.time}</span>
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            <Clock className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No recent activity</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
