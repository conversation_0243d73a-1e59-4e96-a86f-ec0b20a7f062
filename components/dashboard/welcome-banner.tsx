import { <PERSON>, CardContent } from "@/components/ui/card"
import { BookOpen, Users, Video } from "lucide-react"

export function WelcomeBanner() {
  return (
    <Card className="bg-gradient-to-r from-blue-900 to-blue-600 text-white">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Welcome back, <PERSON></h2>
            <p className="text-indigo-100">Continue building your spiritual content library</p>
          </div>
          <div className="flex space-x-4">
            <div className="text-center">
              <BookOpen className="w-8 h-8 mx-auto mb-1" />
              <div className="text-sm">Books</div>
            </div>
            <div className="text-center">
              <Video className="w-8 h-8 mx-auto mb-1" />
              <div className="text-sm">Videos</div>
            </div>
            <div className="text-center">
              <Users className="w-8 h-8 mx-auto mb-1" />
              <div className="text-sm">Team</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
