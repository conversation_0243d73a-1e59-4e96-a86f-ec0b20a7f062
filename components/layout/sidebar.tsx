"use client"

import type React from "react"

import { Home, FolderOpen, Video, FileText, BookOpen, Book, Tags, Library } from "lucide-react"

import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { usePathname } from "next/navigation"

const navigation = [
  { name: "Dashboard", href: "/", icon: Home },
  { name: "Workspaces", href: "/workspaces", icon: FolderOpen },
  { name: "Videos", href: "/videos", icon: Video },
  { name: "Library", href: "/library", icon: Book }, // Consolidated Books/Study Guides/Transcripts
  { name: "Chapters", href: "/chapters", icon: FileText },
  { name: "Subjects", href: "/subjects", icon: Tags },
  { name: "Bible", href: "/bible", icon: BookO<PERSON> },
  { name: "Expository", href: "/expository", icon: Library },
]

interface SidebarProps extends React.HTMLAttributes<HTMLElement> {}

export function Sidebar({ className, ...props }: SidebarProps) {
  const pathname = usePathname()

  return (
    <div className={cn("flex h-full w-[280px] flex-col border-r bg-background py-6", className)} {...props}>
      <ScrollArea className="flex-1 space-y-2 px-3">
        <div className="space-y-2">
          <h2 className="pb-2 pl-4 text-lg font-medium tracking-tight">Acme</h2>
          <ul className="space-y-1">
            {navigation.map((item) => (
              <li key={item.href}>
                <Link href={item.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "justify-start pl-4",
                      pathname === item.href
                        ? "bg-secondary text-muted-foreground hover:bg-secondary hover:text-muted-foreground"
                        : "text-muted-foreground",
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.name}
                  </Button>
                </Link>
              </li>
            ))}
          </ul>
        </div>
        <Separator />
        <div className="space-y-2">
          <h2 className="pb-2 pl-4 text-lg font-medium tracking-tight">Settings</h2>
          <ul className="space-y-1">
            <li>
              <Link href="/account">
                <Button
                  variant="ghost"
                  className={cn(
                    "justify-start pl-4",
                    pathname === "/account"
                      ? "bg-secondary text-muted-foreground hover:bg-secondary hover:text-muted-foreground"
                      : "text-muted-foreground",
                  )}
                >
                  <Avatar className="mr-2 h-4 w-4">
                    <AvatarImage src="/avatars/01.png" />
                    <AvatarFallback>OM</AvatarFallback>
                  </Avatar>
                  Account
                </Button>
              </Link>
            </li>
          </ul>
        </div>
      </ScrollArea>
    </div>
  )
}
