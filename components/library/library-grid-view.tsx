"use client"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Eye,
  Edit,
  Calendar,
  Book,
  GraduationCap,
  FileText,
  Users,
  Clock,
  ExternalLink,
  ShoppingCart,
} from "lucide-react"

interface LibraryItem {
  id: string
  title: string
  type: string
  author?: string
  category?: string
  status: string
  description?: string
  tags?: string[]
  chapters?: number
  duration?: string | null
  targetAudience?: string | null
  estimatedTime?: string | null
  lastUpdated?: string
  event?: string | null
  paperbackLink?: string | null
  ebookLink?: string | null
  faithLibraryLink?: string | null
  amazonLink?: string | null
  inFaithLibrary?: boolean
  inApp?: boolean
}

interface LibraryGridViewProps {
  content: LibraryItem[]
}

export function LibraryGridView({ content }: LibraryGridViewProps) {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Book":
        return <Book className="w-6 h-6 text-blue-600" />
      case "Study Guide":
        return <GraduationCap className="w-6 h-6 text-green-600" />
      case "Transcript":
        return <FileText className="w-6 h-6 text-purple-600" />
      default:
        return <Book className="w-6 h-6 text-gray-600" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Book":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "Study Guide":
        return "bg-green-100 text-green-800 border-green-200"
      case "Transcript":
        return "bg-purple-100 text-purple-800 border-purple-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const hasAnyLink = (item: LibraryItem) => {
    return item.paperbackLink || item.ebookLink || item.faithLibraryLink || item.amazonLink
  }

  // Safety check for content
  if (!content || !Array.isArray(content) || content.length === 0) {
    return (
      <div className="text-center py-12">
        <Book className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
        <p className="text-gray-500">Try adjusting your search or filters to find what you're looking for.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Bookshelf Display */}
      <div className="bg-gradient-to-b from-amber-100 to-amber-200 rounded-lg p-6">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
          {content.map((item) => (
            <div key={`bookshelf-${item.id}`} className="group cursor-pointer">
              <div className="relative">
                <div
                  className={`bg-gradient-to-b ${
                    item.type === "Book"
                      ? "from-blue-600 to-blue-800"
                      : item.type === "Study Guide"
                        ? "from-green-600 to-green-800"
                        : "from-purple-600 to-purple-800"
                  } rounded-sm shadow-lg transform group-hover:scale-105 transition-transform duration-200 h-32 w-24 mx-auto`}
                >
                  <div className="p-2 text-white text-xs">
                    <div className="font-bold line-clamp-3 mb-1">{item.title || "Untitled"}</div>
                    <div className="text-blue-200 text-xs">{item.author || "Unknown Author"}</div>
                  </div>
                  <div className="absolute bottom-1 right-1">
                    <Badge variant="secondary" className="text-xs">
                      {item.type === "Study Guide" ? "SG" : item.type === "Transcript" ? "TR" : "BK"}
                    </Badge>
                  </div>
                  {hasAnyLink(item) && (
                    <div className="absolute top-1 left-1">
                      <ShoppingCart className="w-3 h-3 text-yellow-300" />
                    </div>
                  )}
                </div>
                <div className="text-center mt-2">
                  <div className="text-xs font-medium truncate">{item.title || "Untitled"}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Card View for Additional Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {content.map((item) => (
          <Card key={`card-${item.id}`} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  {getTypeIcon(item.type)}
                  <Badge variant={item.status === "Published" ? "default" : "secondary"}>
                    {item.status || "Draft"}
                  </Badge>
                </div>
                <Badge variant="outline" className={getTypeColor(item.type)}>
                  {item.type || "Book"}
                </Badge>
              </div>

              <h3 className="font-semibold text-lg mb-1 line-clamp-2">{item.title || "Untitled"}</h3>
              <p className="text-sm text-gray-600 mb-2">by {item.author || "Unknown Author"}</p>
              <p className="text-xs text-gray-500 mb-3 line-clamp-2">
                {item.description || "No description available"}
              </p>

              <div className="space-y-2 text-sm">
                {item.type !== "Transcript" && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Chapters:</span>
                    <span className="font-medium">{item.chapters || 0}</span>
                  </div>
                )}

                {item.duration && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Duration:</span>
                    <span className="font-medium">{item.duration}</span>
                  </div>
                )}

                {item.targetAudience && (
                  <div className="flex items-center text-sm">
                    <Users className="w-3 h-3 mr-1 text-blue-600" />
                    <span className="font-medium">{item.targetAudience}</span>
                  </div>
                )}

                {item.estimatedTime && (
                  <div className="flex items-center text-sm">
                    <Clock className="w-3 h-3 mr-1 text-green-600" />
                    <span className="font-medium">{item.estimatedTime}</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-500">Category:</span>
                  <span className="font-medium">{item.category || "General"}</span>
                </div>

                <div className="flex items-center text-gray-500">
                  <Calendar className="w-3 h-3 mr-1" />
                  <span className="text-xs">{item.lastUpdated || "Unknown"}</span>
                </div>

                {item.event && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Event:</span>
                    <span className="font-medium text-xs">{item.event}</span>
                  </div>
                )}
              </div>

              {/* Purchase Links */}
              {hasAnyLink(item) && (
                <div className="mt-3 space-y-1">
                  <div className="text-xs font-medium text-gray-700 mb-2">Available at:</div>
                  <div className="flex flex-wrap gap-1">
                    {item.paperbackLink && (
                      <Button size="sm" variant="outline" className="text-xs h-6 px-2" asChild>
                        <a href={item.paperbackLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-2 h-2 mr-1" />
                          Paperback
                        </a>
                      </Button>
                    )}
                    {item.ebookLink && (
                      <Button size="sm" variant="outline" className="text-xs h-6 px-2" asChild>
                        <a href={item.ebookLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-2 h-2 mr-1" />
                          E-Book
                        </a>
                      </Button>
                    )}
                    {item.faithLibraryLink && (
                      <Button size="sm" variant="outline" className="text-xs h-6 px-2" asChild>
                        <a href={item.faithLibraryLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-2 h-2 mr-1" />
                          Faith Library
                        </a>
                      </Button>
                    )}
                    {item.amazonLink && (
                      <Button size="sm" variant="outline" className="text-xs h-6 px-2" asChild>
                        <a href={item.amazonLink} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-2 h-2 mr-1" />
                          Amazon
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {item.inFaithLibrary && (
                <div className="mt-2">
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Faith Library
                  </Badge>
                </div>
              )}

              {item.inApp && (
                <div className="mt-1">
                  <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                    Mobile App
                  </Badge>
                </div>
              )}

              {/* Safe tags rendering */}
              {item.tags && Array.isArray(item.tags) && item.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3 mb-3">
                  {item.tags.slice(0, 2).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {item.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{item.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}

              <div className="flex space-x-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Named export for compatibility
export { LibraryGridView as default }
