"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, ArrowUpDown, ArrowUp, ArrowDown, Book, GraduationCap, FileText } from "lucide-react"
import type { LibraryItem } from "@/app/actions/library"

interface LibraryListViewProps {
  content: LibraryItem[]
  sortBy: string
  setSortBy: (field: string) => void
  sortOrder: "asc" | "desc"
  setSortOrder: (order: "asc" | "desc") => void
}

export function LibraryListView({ content, sortBy, setSortBy, sortOrder, setSortOrder }: LibraryListViewProps) {
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("asc")
    }
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return <ArrowUpDown className="w-4 h-4" />
    return sortOrder === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Book":
        return <Book className="w-4 h-4 text-blue-600" />
      case "Study Guide":
        return <GraduationCap className="w-4 h-4 text-green-600" />
      case "Transcript":
        return <FileText className="w-4 h-4 text-purple-600" />
      default:
        return <Book className="w-4 h-4 text-gray-600" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Book":
        return "bg-blue-100 text-blue-800"
      case "Study Guide":
        return "bg-green-100 text-green-800"
      case "Transcript":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="bg-white rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">Type</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("title")} className="h-auto p-0 font-semibold">
                Title <SortIcon field="title" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("author")} className="h-auto p-0 font-semibold">
                Author <SortIcon field="author" />
              </Button>
            </TableHead>
            <TableHead>Content</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("category")} className="h-auto p-0 font-semibold">
                Category <SortIcon field="category" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("status")} className="h-auto p-0 font-semibold">
                Status <SortIcon field="status" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("lastUpdated")} className="h-auto p-0 font-semibold">
                Updated <SortIcon field="lastUpdated" />
              </Button>
            </TableHead>
            <TableHead>Tags</TableHead>
            <TableHead>Event</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {content.map((item) => (
            <TableRow key={item.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="flex items-center justify-center">{getTypeIcon(item.type)}</div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{item.title}</div>
                  <div className="text-sm text-gray-500 line-clamp-1">{item.description}</div>
                </div>
              </TableCell>
              <TableCell>{item.author}</TableCell>
              <TableCell>
                <div className="space-y-1">
                  <Badge variant="outline" className={getTypeColor(item.type)}>
                    {item.type}
                  </Badge>
                  {item.type !== "Transcript" && <div className="text-xs text-gray-500">{item.chapters} chapters</div>}
                  {item.duration && <div className="text-xs text-gray-500">{item.duration}</div>}
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <Badge variant={item.status === "Published" ? "default" : "secondary"}>{item.status}</Badge>
                  {item.inFaithLibrary && (
                    <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                      Faith Library
                    </Badge>
                  )}
                  {item.inApp && (
                    <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                      Mobile App
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-sm text-gray-500">{item.lastUpdated}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {item.tags.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{item.tags.length - 2}
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-sm">{item.event || "-"}</TableCell>
              <TableCell>
                <div className="flex space-x-1">
                  <Button size="sm" variant="outline">
                    <Eye className="w-3 h-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Edit className="w-3 h-3" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
