"use client"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Search, BookOpen, Video, FileText, Users, Tags, Book, Library, Play, Calendar, User } from "lucide-react"

interface SearchResult {
  id: string
  title: string
  type: "book" | "chapter" | "video" | "study-guide" | "transcript" | "subject" | "workspace"
  description?: string
  author?: string
  minister?: string
  duration?: string
  book?: string
  contentCount?: number
  collaborators?: number
  date?: string
  status?: string
}

export function MasterSearch() {
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const searchContent = async () => {
      if (query.length < 2) {
        setResults([])
        setShowResults(false)
        return
      }

      setIsLoading(true)
      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`)
        if (response.ok) {
          const data = await response.json()
          setResults(data.results || [])
          setShowResults(true)
        } else {
          setResults([])
        }
      } catch (error) {
        console.error("Search error:", error)
        setResults([])
      } finally {
        setIsLoading(false)
      }
    }

    const debounceTimer = setTimeout(searchContent, 300)
    return () => clearTimeout(debounceTimer)
  }, [query])

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const getIcon = (type: string) => {
    switch (type) {
      case "book":
        return <BookOpen className="w-4 h-4" />
      case "study-guide":
        return <Book className="w-4 h-4" />
      case "chapter":
        return <FileText className="w-4 h-4" />
      case "video":
        return <Video className="w-4 h-4" />
      case "transcript":
        return <FileText className="w-4 h-4" />
      case "subject":
        return <Tags className="w-4 h-4" />
      case "workspace":
        return <Users className="w-4 h-4" />
      default:
        return <Library className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "book":
        return "bg-blue-100 text-blue-800"
      case "study-guide":
        return "bg-green-100 text-green-800"
      case "chapter":
        return "bg-purple-100 text-purple-800"
      case "video":
        return "bg-red-100 text-red-800"
      case "transcript":
        return "bg-orange-100 text-orange-800"
      case "subject":
        return "bg-yellow-100 text-yellow-800"
      case "workspace":
        return "bg-indigo-100 text-indigo-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div ref={searchRef} className="relative w-full max-w-4xl">
      <div className="relative">
        <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6" />
        <Input
          placeholder="Search books, chapters, videos, study guides, transcripts, and more..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-16 pr-6 py-6 text-xl border-2 border-gray-300 focus:border-amber-500 rounded-2xl shadow-xl bg-white/90 backdrop-blur-sm"
        />
        {isLoading && (
          <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-amber-600"></div>
          </div>
        )}
      </div>

      {showResults && results.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-4 max-h-[500px] overflow-auto z-50 shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardContent className="p-0">
            <div className="p-4 border-b bg-gray-50/80">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Found {results.length} results for "{query}"
                </span>
                <Button variant="ghost" size="sm" className="text-amber-600 hover:text-amber-700">
                  View all results
                </Button>
              </div>
            </div>

            <div className="divide-y divide-gray-100">
              {results.map((result) => (
                <div key={result.id} className="p-4 hover:bg-gray-50/50 cursor-pointer transition-colors">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getTypeColor(result.type)}`}>{getIcon(result.type)}</div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-900 truncate">{result.title}</h3>
                        <Badge variant="outline" className="text-xs capitalize">
                          {result.type.replace("-", " ")}
                        </Badge>
                      </div>

                      {result.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">{result.description}</p>
                      )}

                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        {result.author && (
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{result.author}</span>
                          </div>
                        )}
                        {result.minister && (
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{result.minister}</span>
                          </div>
                        )}
                        {result.book && (
                          <div className="flex items-center space-x-1">
                            <BookOpen className="w-3 h-3" />
                            <span>from {result.book}</span>
                          </div>
                        )}
                        {result.duration && (
                          <div className="flex items-center space-x-1">
                            <Play className="w-3 h-3" />
                            <span>{result.duration}</span>
                          </div>
                        )}
                        {result.date && (
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{new Date(result.date).toLocaleDateString()}</span>
                          </div>
                        )}
                        {result.contentCount && <span>{result.contentCount} items</span>}
                        {result.collaborators && <span>{result.collaborators} collaborators</span>}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="p-4 border-t bg-gray-50/80">
              <Button className="w-full bg-amber-600 hover:bg-amber-700 text-white">
                Search all content for "{query}"
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {showResults && results.length === 0 && !isLoading && query.length >= 2 && (
        <Card className="absolute top-full left-0 right-0 mt-4 z-50 shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
          <CardContent className="p-8 text-center">
            <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600">
              Try searching for books, chapters, videos, or study guides with different keywords.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
