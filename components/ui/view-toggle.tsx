"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Grid3X3, List } from "lucide-react"
import { cn } from "@/lib/utils"

interface ViewToggleProps {
  view: "grid" | "list"
  onViewChange: (view: "grid" | "list") => void
}

export function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="flex items-center border rounded-lg p-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("grid")}
        className={cn("h-8 w-8 p-0", view === "grid" && "bg-blue-100 text-blue-700")}
      >
        <Grid3X3 className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onViewChange("list")}
        className={cn("h-8 w-8 p-0", view === "list" && "bg-blue-100 text-blue-700")}
      >
        <List className="w-4 h-4" />
      </Button>
    </div>
  )
}
