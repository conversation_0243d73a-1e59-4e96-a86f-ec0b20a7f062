"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Play, FileText, User, Calendar, Eye } from "lucide-react"

interface Video {
  id: number
  title: string
  minister: string
  duration: string
  publishedDate: string
  transcriptionStatus: string
  transcriptionProgress?: number
  subjects: string[]
  thumbnail: string
  views: number
  description: string
}

interface VideosGridViewProps {
  videos: Video[]
}

export function VideosGridView({ videos }: VideosGridViewProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {videos.map((video) => (
        <Card key={video.id} className="hover:shadow-lg transition-shadow">
          <div className="relative">
            <img
              src={video.thumbnail || "/placeholder.svg"}
              alt={video.title}
              className="w-full h-32 object-cover rounded-t-lg"
            />
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded-t-lg">
              <Button size="icon" className="bg-white text-black hover:bg-gray-100">
                <Play className="w-6 h-6" />
              </Button>
            </div>
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
              {video.duration}
            </div>
            <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center">
              <Eye className="w-3 h-3 mr-1" />
              {video.views}
            </div>
          </div>

          <CardHeader className="pb-3">
            <CardTitle className="text-lg line-clamp-2">{video.title}</CardTitle>
            <div className="flex items-center text-sm text-gray-600">
              <User className="w-4 h-4 mr-1" />
              {video.minister}
            </div>
          </CardHeader>

          <CardContent className="space-y-3">
            <p className="text-sm text-gray-600 line-clamp-2">{video.description}</p>

            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="w-4 h-4 mr-1" />
              {video.publishedDate}
            </div>

            {/* Transcription Status */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Transcription</span>
                <Badge
                  variant={
                    video.transcriptionStatus === "completed"
                      ? "default"
                      : video.transcriptionStatus === "in-progress"
                        ? "secondary"
                        : "outline"
                  }
                >
                  {video.transcriptionStatus}
                </Badge>
              </div>

              {video.transcriptionStatus === "in-progress" && video.transcriptionProgress && (
                <div className="space-y-1">
                  <Progress value={video.transcriptionProgress} className="h-2" />
                  <span className="text-xs text-gray-500">{video.transcriptionProgress}% complete</span>
                </div>
              )}
            </div>

            {/* Subjects */}
            <div className="flex flex-wrap gap-1">
              {video.subjects.map((subject) => (
                <Badge key={subject} variant="outline" className="text-xs">
                  {subject}
                </Badge>
              ))}
            </div>

            {/* Actions */}
            <div className="flex space-x-2 pt-2">
              <Button size="sm" variant="outline" className="flex-1">
                <Play className="w-4 h-4 mr-1" />
                Watch
              </Button>
              {video.transcriptionStatus === "completed" && (
                <Button size="sm" variant="outline" className="flex-1">
                  <FileText className="w-4 h-4 mr-1" />
                  Transcript
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
