"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Play, FileText, ArrowUpDown, ArrowUp, ArrowDown, Eye } from "lucide-react"

interface Video {
  id: number
  title: string
  minister: string
  duration: string
  publishedDate: string
  transcriptionStatus: string
  transcriptionProgress?: number
  subjects: string[]
  thumbnail: string
  views: number
  description: string
}

interface VideosListViewProps {
  videos: Video[]
  sortBy: string
  setSortBy: (field: string) => void
  sortOrder: "asc" | "desc"
  setSortOrder: (order: "asc" | "desc") => void
}

export function VideosListView({ videos, sortBy, setSortBy, sortOrder, setSortOrder }: VideosListViewProps) {
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc")
    } else {
      setSortBy(field)
      setSortOrder("asc")
    }
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return <ArrowUpDown className="w-4 h-4" />
    return sortOrder === "asc" ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />
  }

  return (
    <div className="bg-white rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-20">Thumbnail</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("title")} className="h-auto p-0 font-semibold">
                Title <SortIcon field="title" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("minister")} className="h-auto p-0 font-semibold">
                Minister <SortIcon field="minister" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("duration")} className="h-auto p-0 font-semibold">
                Duration <SortIcon field="duration" />
              </Button>
            </TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("views")} className="h-auto p-0 font-semibold">
                Views <SortIcon field="views" />
              </Button>
            </TableHead>
            <TableHead>Transcription</TableHead>
            <TableHead>
              <Button variant="ghost" onClick={() => handleSort("publishedDate")} className="h-auto p-0 font-semibold">
                Published <SortIcon field="publishedDate" />
              </Button>
            </TableHead>
            <TableHead>Subjects</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {videos.map((video) => (
            <TableRow key={video.id} className="hover:bg-gray-50">
              <TableCell>
                <div className="relative w-16 h-10">
                  <img
                    src={video.thumbnail || "/placeholder.svg"}
                    alt={video.title}
                    className="w-full h-full object-cover rounded"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity rounded">
                    <Play className="w-4 h-4 text-white" />
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium line-clamp-1">{video.title}</div>
                  <div className="text-sm text-gray-500 line-clamp-1">{video.description}</div>
                </div>
              </TableCell>
              <TableCell>{video.minister}</TableCell>
              <TableCell className="font-mono text-sm">{video.duration}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Eye className="w-3 h-3 mr-1 text-gray-400" />
                  {video.views.toLocaleString()}
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <Badge
                    variant={
                      video.transcriptionStatus === "completed"
                        ? "default"
                        : video.transcriptionStatus === "in-progress"
                          ? "secondary"
                          : "outline"
                    }
                    className="text-xs"
                  >
                    {video.transcriptionStatus}
                  </Badge>
                  {video.transcriptionStatus === "in-progress" && video.transcriptionProgress && (
                    <div className="w-20">
                      <Progress value={video.transcriptionProgress} className="h-1" />
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-sm text-gray-500">{video.publishedDate}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {video.subjects.slice(0, 2).map((subject) => (
                    <Badge key={subject} variant="outline" className="text-xs">
                      {subject}
                    </Badge>
                  ))}
                  {video.subjects.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{video.subjects.length - 2}
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex space-x-1">
                  <Button size="sm" variant="outline">
                    <Play className="w-3 h-3" />
                  </Button>
                  {video.transcriptionStatus === "completed" && (
                    <Button size="sm" variant="outline">
                      <FileText className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
