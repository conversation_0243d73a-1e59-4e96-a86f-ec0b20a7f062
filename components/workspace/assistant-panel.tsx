"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Sparkles, FileText, List, BookOpen, Send } from "lucide-react"

export function AssistantPanel() {
  const [prompt, setPrompt] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async () => {
    if (!prompt.trim()) return
    setIsLoading(true)
    // Simulate AI processing
    setTimeout(() => {
      setIsLoading(false)
      setPrompt("")
    }, 2000)
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900 flex items-center">
          <Sparkles className="w-4 h-4 mr-2 text-blue-600" />
          AI Assistant
        </h3>
        <p className="text-sm text-gray-600">Get help with your content</p>
      </div>

      <Tabs defaultValue="summarize" className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3 mx-4 mt-4">
          <TabsTrigger value="summarize" className="text-xs">
            <FileText className="w-3 h-3 mr-1" />
            Summarize
          </TabsTrigger>
          <TabsTrigger value="outline" className="text-xs">
            <List className="w-3 h-3 mr-1" />
            Outline
          </TabsTrigger>
          <TabsTrigger value="planner" className="text-xs">
            <BookOpen className="w-3 h-3 mr-1" />
            Planner
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-auto p-4">
          <TabsContent value="summarize" className="space-y-4 mt-0">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Summarize Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Textarea
                  placeholder="Paste content to summarize or select sources from the tray..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[100px]"
                />
                <Button onClick={handleSubmit} disabled={isLoading || !prompt.trim()} className="w-full" size="sm">
                  {isLoading ? (
                    "Summarizing..."
                  ) : (
                    <>
                      <Send className="w-3 h-3 mr-1" />
                      Summarize
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Recent Summaries</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="p-2 bg-blue-50 rounded text-xs">
                    <p className="font-medium mb-1">Faith Chapter Summary</p>
                    <p className="text-gray-600">
                      Faith encompasses both belief and action, requiring trust in God's promises even when
                      circumstances seem uncertain...
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="outline" className="space-y-4 mt-0">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Generate Outline</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Textarea placeholder="Describe your topic or paste content to outline..." className="min-h-[100px]" />
                <Button className="w-full" size="sm">
                  <List className="w-3 h-3 mr-1" />
                  Create Outline
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="planner" className="space-y-4 mt-0">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Lesson Planner</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <label className="text-xs font-medium">Topic</label>
                  <Textarea placeholder="What is the main topic?" className="min-h-[60px]" />
                </div>
                <div className="space-y-2">
                  <label className="text-xs font-medium">Objective</label>
                  <Textarea placeholder="What should learners understand?" className="min-h-[60px]" />
                </div>
                <div className="space-y-2">
                  <label className="text-xs font-medium">Key Scripture</label>
                  <Textarea placeholder="Primary Bible verse or passage" className="min-h-[60px]" />
                </div>
                <Button className="w-full" size="sm">
                  <BookOpen className="w-3 h-3 mr-1" />
                  Generate Plan
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
