"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Bold, Italic, Underline, List, Quote, Link } from "lucide-react"

export function RichEditor() {
  const [title, setTitle] = useState("Day 1: The Foundation of Faith")
  const [content, setContent] = useState(`# Introduction

Faith is the cornerstone of our spiritual journey. In this devotional, we'll explore what it means to have unwavering faith in God's promises.

## Key Scripture
"Now faith is confidence in what we hope for and assurance about what we do not see." - Hebrews 11:1

## Reflection
Consider a time when you had to trust God despite uncertain circumstances...`)

  return (
    <div className="h-full flex flex-col">
      {/* Editor Toolbar */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Bold className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Italic className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Underline className="w-4 h-4" />
          </Button>
          <div className="w-px h-6 bg-gray-300 mx-2" />
          <Button variant="ghost" size="sm">
            <List className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Quote className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Link className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 p-6 overflow-auto">
        <div className="max-w-4xl mx-auto space-y-4">
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="text-2xl font-bold border-none p-0 focus:ring-0"
            placeholder="Enter title..."
          />

          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="min-h-[600px] border-none p-0 resize-none focus:ring-0 text-base leading-relaxed"
            placeholder="Start writing your content..."
          />
        </div>
      </div>
    </div>
  )
}
