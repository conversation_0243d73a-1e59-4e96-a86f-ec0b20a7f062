"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { BookOpen, Video, Book, MessageSquare, Plus } from "lucide-react"

const sources = {
  chapters: [
    {
      id: 1,
      title: "Faith and Healing - Chapter 12",
      excerpt: "Faith is not just belief, but active trust in God's promises...",
      type: "chapter",
    },
    {
      id: 2,
      title: "Foundations - Chapter 3",
      excerpt: "The biblical foundation of faith begins with understanding...",
      type: "chapter",
    },
  ],
  videos: [
    {
      id: 1,
      title: "Sunday Service - Faith in Action",
      excerpt: "Pastor <PERSON> discusses practical applications of faith...",
      type: "video",
      duration: "45:32",
    },
  ],
  bible: [
    {
      id: 1,
      reference: "Hebrews 11:1",
      text: "Now faith is confidence in what we hope for and assurance about what we do not see.",
      type: "verse",
    },
    {
      id: 2,
      reference: "Romans 10:17",
      text: "So faith comes from hearing, and hearing through the word of Christ.",
      type: "verse",
    },
  ],
  ai: [
    {
      id: 1,
      title: "Faith Summary",
      content: "Faith in biblical context encompasses both intellectual assent and practical trust...",
      type: "summary",
    },
  ],
}

export function SourceTray() {
  const [activeTab, setActiveTab] = useState("chapters")

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Source Library</h3>
        <p className="text-sm text-gray-600">Drag sources into your workspace</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
          <TabsTrigger value="chapters" className="text-xs">
            <BookOpen className="w-3 h-3 mr-1" />
            Chapters
          </TabsTrigger>
          <TabsTrigger value="videos" className="text-xs">
            <Video className="w-3 h-3 mr-1" />
            Videos
          </TabsTrigger>
          <TabsTrigger value="bible" className="text-xs">
            <Book className="w-3 h-3 mr-1" />
            Bible
          </TabsTrigger>
          <TabsTrigger value="ai" className="text-xs">
            <MessageSquare className="w-3 h-3 mr-1" />
            AI
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-auto p-4">
          <TabsContent value="chapters" className="space-y-3 mt-0">
            {sources.chapters.map((source) => (
              <Card key={source.id} className="hover:shadow-sm transition-shadow">
                <CardContent className="p-3">
                  <h4 className="font-medium text-sm mb-1">{source.title}</h4>
                  <p className="text-xs text-gray-600 mb-2 line-clamp-2">{source.excerpt}</p>
                  <div className="flex justify-between items-center">
                    <Badge variant="outline" className="text-xs">
                      Chapter
                    </Badge>
                    <Button size="sm" variant="outline" className="text-xs h-6">
                      <Plus className="w-3 h-3 mr-1" />
                      Insert
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="videos" className="space-y-3 mt-0">
            {sources.videos.map((source) => (
              <Card key={source.id} className="hover:shadow-sm transition-shadow">
                <CardContent className="p-3">
                  <h4 className="font-medium text-sm mb-1">{source.title}</h4>
                  <p className="text-xs text-gray-600 mb-2">{source.excerpt}</p>
                  <div className="flex justify-between items-center">
                    <Badge variant="outline" className="text-xs">
                      {source.duration}
                    </Badge>
                    <Button size="sm" variant="outline" className="text-xs h-6">
                      <Plus className="w-3 h-3 mr-1" />
                      Insert
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="bible" className="space-y-3 mt-0">
            {sources.bible.map((source) => (
              <Card key={source.id} className="hover:shadow-sm transition-shadow">
                <CardContent className="p-3">
                  <h4 className="font-medium text-sm mb-1">{source.reference}</h4>
                  <p className="text-xs text-gray-600 mb-2 italic">"{source.text}"</p>
                  <div className="flex justify-between items-center">
                    <Badge variant="outline" className="text-xs">
                      Verse
                    </Badge>
                    <Button size="sm" variant="outline" className="text-xs h-6">
                      <Plus className="w-3 h-3 mr-1" />
                      Insert
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="ai" className="space-y-3 mt-0">
            {sources.ai.map((source) => (
              <Card key={source.id} className="hover:shadow-sm transition-shadow">
                <CardContent className="p-3">
                  <h4 className="font-medium text-sm mb-1">{source.title}</h4>
                  <p className="text-xs text-gray-600 mb-2 line-clamp-3">{source.content}</p>
                  <div className="flex justify-between items-center">
                    <Badge variant="outline" className="text-xs">
                      AI Summary
                    </Badge>
                    <Button size="sm" variant="outline" className="text-xs h-6">
                      <Plus className="w-3 h-3 mr-1" />
                      Insert
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
}
