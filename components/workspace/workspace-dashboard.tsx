"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { AddWorkspaceModal } from "./add-workspace-modal"
import { Plus, Search, FileText, Users, Calendar, Edit } from "lucide-react"

interface WorkspaceDashboardProps {
  onSelectWorkspace: (id: string) => void
}

const workspaces = [
  {
    id: "1",
    title: "Devotional Series - Faith",
    description: "A 30-day devotional series exploring the foundations of faith",
    collaborators: ["<PERSON>", "<PERSON>"],
    lastModified: "2 hours ago",
    sources: 12,
    status: "active",
    tags: ["Devotional", "Faith", "Series"],
  },
  {
    id: "2",
    title: "Youth Study Guide - Love",
    description: "Interactive study guide for youth ministry on biblical love",
    collaborators: ["<PERSON>"],
    lastModified: "1 day ago",
    sources: 8,
    status: "draft",
    tags: ["Youth", "Love", "Study Guide"],
  },
  {
    id: "3",
    title: "Sermon Series - Holiness",
    description: "Comprehensive sermon series on biblical holiness and sanctification",
    collaborators: ["John Doe", "Elder Smith", "Pastor Brown"],
    lastModified: "3 days ago",
    sources: 24,
    status: "review",
    tags: ["Sermon", "Holiness", "Sanctification"],
  },
]

export function WorkspaceDashboard({ onSelectWorkspace }: WorkspaceDashboardProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddModal, setShowAddModal] = useState(false)

  const filteredWorkspaces = workspaces.filter(
    (workspace) =>
      workspace.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workspace.description.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Writer's Studio</h1>
          <p className="text-gray-600">Create, organize, and collaborate on spiritual content</p>
        </div>
        <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          New Workspace
        </Button>
      </div>

      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder="Search workspaces..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWorkspaces.map((workspace) => (
          <Card
            key={workspace.id}
            className="hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => onSelectWorkspace(workspace.id)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <FileText className="w-8 h-8 text-blue-600" />
                <Badge
                  variant={
                    workspace.status === "active" ? "default" : workspace.status === "draft" ? "secondary" : "outline"
                  }
                >
                  {workspace.status}
                </Badge>
              </div>
              <CardTitle className="text-lg">{workspace.title}</CardTitle>
              <p className="text-sm text-gray-600 line-clamp-2">{workspace.description}</p>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500">Sources:</span>
                <span className="font-medium">{workspace.sources}</span>
              </div>

              <div className="flex items-center text-sm text-gray-500">
                <Users className="w-4 h-4 mr-1" />
                {workspace.collaborators.length} collaborator{workspace.collaborators.length !== 1 ? "s" : ""}
              </div>

              <div className="flex items-center text-sm text-gray-500">
                <Calendar className="w-4 h-4 mr-1" />
                {workspace.lastModified}
              </div>

              <div className="flex flex-wrap gap-1">
                {workspace.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>

              <Button size="sm" variant="outline" className="w-full mt-3">
                <Edit className="w-4 h-4 mr-1" />
                Open Workspace
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <AddWorkspaceModal open={showAddModal} onOpenChange={setShowAddModal} />
    </div>
  )
}
