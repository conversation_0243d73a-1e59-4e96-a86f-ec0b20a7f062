"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Save, Share, Download, Settings } from "lucide-react"
import { RichEditor } from "./rich-editor"
import { SourceTray } from "./source-tray"
import { AssistantPanel } from "./assistant-panel"

interface WorkspaceViewProps {
  workspaceId: string
  onBack: () => void
}

export function WorkspaceView({ workspaceId, onBack }: WorkspaceViewProps) {
  const [showAssistant, setShowAssistant] = useState(true)
  const [showSources, setShowSources] = useState(true)

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div>
              <h1 className="text-xl font-semibold">Devotional Series - Faith</h1>
              <p className="text-sm text-gray-600">Last saved 2 minutes ago</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Share className="w-4 h-4 mr-1" />
              Share
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-1" />
              Settings
            </Button>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Source Tray */}
        {showSources && (
          <div className="w-80 bg-white border-r border-gray-200">
            <SourceTray />
          </div>
        )}

        {/* Editor */}
        <div className="flex-1 bg-white">
          <RichEditor />
        </div>

        {/* Assistant Panel */}
        {showAssistant && (
          <div className="w-80 bg-gray-50 border-l border-gray-200">
            <AssistantPanel />
          </div>
        )}
      </div>
    </div>
  )
}
