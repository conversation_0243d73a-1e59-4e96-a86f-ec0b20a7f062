// Simple auth implementation
export interface User {
  id: string
  email: string
  name: string
  role: "admin" | "author" | "editor" | "viewer"
}

export interface Session {
  user: User | null
}

// Export the required auth function
export async function auth(): Promise<Session> {
  // For development, return a mock admin user
  return {
    user: {
      id: "1",
      email: "<EMAIL>",
      name: "Admin User",
      role: "admin",
    },
  }
}

export async function signIn(credentials: { email: string; password: string }) {
  return { success: true }
}

export async function signOut() {
  return { success: true }
}

export const handlers = {
  GET: async () => new Response("Auth handler"),
  POST: async () => new Response("Auth handler"),
}
