// Bible API service using Bible.com API (YouVersion) - KJV Version
const BIBLE_API_BASE = "https://bible-api.com"

export interface BibleVerse {
  book_id: string
  book_name: string
  chapter: number
  verse: number
  text: string
}

export interface BibleChapter {
  reference: string
  verses: BibleVerse[]
  text: string
  translation_id: string
  translation_name: string
  translation_note: string
}

export interface BibleBook {
  id: string
  name: string
  testament: "old" | "new"
  chapters: number
}

// Bible books data - KJV names
export const BIBLE_BOOKS: BibleBook[] = [
  // Old Testament
  { id: "genesis", name: "Genesis", testament: "old", chapters: 50 },
  { id: "exodus", name: "<PERSON>", testament: "old", chapters: 40 },
  { id: "leviticus", name: "<PERSON><PERSON>", testament: "old", chapters: 27 },
  { id: "numbers", name: "Numbers", testament: "old", chapters: 36 },
  { id: "deuteronomy", name: "Deuteronomy", testament: "old", chapters: 34 },
  { id: "joshua", name: "<PERSON>", testament: "old", chapters: 24 },
  { id: "judges", name: "<PERSON>", testament: "old", chapters: 21 },
  { id: "ruth", name: "<PERSON>", testament: "old", chapters: 4 },
  { id: "1samuel", name: "1 Samuel", testament: "old", chapters: 31 },
  { id: "2samuel", name: "2 Samuel", testament: "old", chapters: 24 },
  { id: "1kings", name: "1 Kings", testament: "old", chapters: 22 },
  { id: "2kings", name: "2 Kings", testament: "old", chapters: 25 },
  { id: "1chronicles", name: "1 Chronicles", testament: "old", chapters: 29 },
  { id: "2chronicles", name: "2 Chronicles", testament: "old", chapters: 36 },
  { id: "ezra", name: "Ezra", testament: "old", chapters: 10 },
  { id: "nehemiah", name: "Nehemiah", testament: "old", chapters: 13 },
  { id: "esther", name: "Esther", testament: "old", chapters: 10 },
  { id: "job", name: "Job", testament: "old", chapters: 42 },
  { id: "psalms", name: "Psalms", testament: "old", chapters: 150 },
  { id: "proverbs", name: "Proverbs", testament: "old", chapters: 31 },
  { id: "ecclesiastes", name: "Ecclesiastes", testament: "old", chapters: 12 },
  { id: "song", name: "Song of Solomon", testament: "old", chapters: 8 },
  { id: "isaiah", name: "Isaiah", testament: "old", chapters: 66 },
  { id: "jeremiah", name: "Jeremiah", testament: "old", chapters: 52 },
  { id: "lamentations", name: "Lamentations", testament: "old", chapters: 5 },
  { id: "ezekiel", name: "Ezekiel", testament: "old", chapters: 48 },
  { id: "daniel", name: "Daniel", testament: "old", chapters: 12 },
  { id: "hosea", name: "Hosea", testament: "old", chapters: 14 },
  { id: "joel", name: "Joel", testament: "old", chapters: 3 },
  { id: "amos", name: "Amos", testament: "old", chapters: 9 },
  { id: "obadiah", name: "Obadiah", testament: "old", chapters: 1 },
  { id: "jonah", name: "Jonah", testament: "old", chapters: 4 },
  { id: "micah", name: "Micah", testament: "old", chapters: 7 },
  { id: "nahum", name: "Nahum", testament: "old", chapters: 3 },
  { id: "habakkuk", name: "Habakkuk", testament: "old", chapters: 3 },
  { id: "zephaniah", name: "Zephaniah", testament: "old", chapters: 3 },
  { id: "haggai", name: "Haggai", testament: "old", chapters: 2 },
  { id: "zechariah", name: "Zechariah", testament: "old", chapters: 14 },
  { id: "malachi", name: "Malachi", testament: "old", chapters: 4 },

  // New Testament
  { id: "matthew", name: "Matthew", testament: "new", chapters: 28 },
  { id: "mark", name: "Mark", testament: "new", chapters: 16 },
  { id: "luke", name: "Luke", testament: "new", chapters: 24 },
  { id: "john", name: "John", testament: "new", chapters: 21 },
  { id: "acts", name: "Acts", testament: "new", chapters: 28 },
  { id: "romans", name: "Romans", testament: "new", chapters: 16 },
  { id: "1corinthians", name: "1 Corinthians", testament: "new", chapters: 16 },
  { id: "2corinthians", name: "2 Corinthians", testament: "new", chapters: 13 },
  { id: "galatians", name: "Galatians", testament: "new", chapters: 6 },
  { id: "ephesians", name: "Ephesians", testament: "new", chapters: 6 },
  { id: "philippians", name: "Philippians", testament: "new", chapters: 4 },
  { id: "colossians", name: "Colossians", testament: "new", chapters: 4 },
  { id: "1thessalonians", name: "1 Thessalonians", testament: "new", chapters: 5 },
  { id: "2thessalonians", name: "2 Thessalonians", testament: "new", chapters: 3 },
  { id: "1timothy", name: "1 Timothy", testament: "new", chapters: 6 },
  { id: "2timothy", name: "2 Timothy", testament: "new", chapters: 4 },
  { id: "titus", name: "Titus", testament: "new", chapters: 3 },
  { id: "philemon", name: "Philemon", testament: "new", chapters: 1 },
  { id: "hebrews", name: "Hebrews", testament: "new", chapters: 13 },
  { id: "james", name: "James", testament: "new", chapters: 5 },
  { id: "1peter", name: "1 Peter", testament: "new", chapters: 5 },
  { id: "2peter", name: "2 Peter", testament: "new", chapters: 3 },
  { id: "1john", name: "1 John", testament: "new", chapters: 5 },
  { id: "2john", name: "2 John", testament: "new", chapters: 1 },
  { id: "3john", name: "3 John", testament: "new", chapters: 1 },
  { id: "jude", name: "Jude", testament: "new", chapters: 1 },
  { id: "revelation", name: "Revelation", testament: "new", chapters: 22 },
]

export class BibleAPI {
  private static instance: BibleAPI
  private cache: Map<string, any> = new Map()

  static getInstance(): BibleAPI {
    if (!BibleAPI.instance) {
      BibleAPI.instance = new BibleAPI()
    }
    return BibleAPI.instance
  }

  async getVerse(reference: string): Promise<BibleChapter | null> {
    try {
      // Check cache first
      if (this.cache.has(reference)) {
        return this.cache.get(reference)
      }

      // Add KJV parameter to the API call
      const response = await fetch(`${BIBLE_API_BASE}/${encodeURIComponent(reference)}?translation=kjv`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Ensure we have KJV translation info
      if (data) {
        data.translation_name = "King James Version"
        data.translation_id = "kjv"
        data.translation_note =
          "The King James Version (KJV), also known as the Authorized Version, is an English translation of the Christian Bible for the Church of England, commissioned in 1604 and completed in 1611."
      }

      // Cache the result
      this.cache.set(reference, data)

      return data
    } catch (error) {
      console.error("Error fetching Bible verse:", error)
      return null
    }
  }

  async getChapter(book: string, chapter: number): Promise<BibleChapter | null> {
    const reference = `${book} ${chapter}`
    return this.getVerse(reference)
  }

  async searchVerses(query: string): Promise<BibleVerse[]> {
    try {
      // For now, we'll implement a simple search by fetching popular KJV verses
      const searchResults: BibleVerse[] = []

      // Popular KJV verses for search
      const popularVerses = [
        "John 3:16",
        "Romans 8:28",
        "Philippians 4:13",
        "Jeremiah 29:11",
        "Psalm 23:1",
        "Isaiah 41:10",
        "Matthew 28:19",
        "Ephesians 2:8-9",
        "1 Corinthians 13:4-7",
        "Proverbs 3:5-6",
        "Romans 10:9",
        "John 14:6",
        "Psalm 119:105",
        "2 Timothy 3:16",
        "1 John 1:9",
      ]

      for (const verse of popularVerses) {
        const result = await this.getVerse(verse)
        if (result && result.text.toLowerCase().includes(query.toLowerCase())) {
          searchResults.push(...result.verses)
        }
      }

      return searchResults
    } catch (error) {
      console.error("Error searching verses:", error)
      return []
    }
  }

  parseReference(reference: string): { book: string; chapter?: number; verse?: number } | null {
    // Parse references like "John 3:16", "Romans 8", "1 Corinthians 13:4-7"
    const match = reference.match(/^(\d?\s?\w+)\s+(\d+)(?::(\d+)(?:-(\d+))?)?$/i)

    if (!match) return null

    return {
      book: match[1].trim(),
      chapter: Number.parseInt(match[2]),
      verse: match[3] ? Number.parseInt(match[3]) : undefined,
    }
  }

  formatReference(book: string, chapter: number, verse?: number): string {
    if (verse) {
      return `${book} ${chapter}:${verse}`
    }
    return `${book} ${chapter}`
  }
}

export const bibleAPI = BibleAPI.getInstance()
