import { neon } from "@neondatabase/serverless"
import { isDatabaseConfigured } from "@/lib/database"

// Export Book type for use in components
export interface Book {
  id: string
  title: string
  author: string
  type: string
  chapters: number
  status: string
  category: string
  lastUpdated: string
  tags: string[]
  cover: string
  description: string
}

export async function getBooks(
  searchTerm = "",
  type = "all",
  category = "all",
  status = "all",
  sortBy = "title",
  sortOrder: "asc" | "desc" = "asc",
): Promise<Book[]> {
  if (!isDatabaseConfigured()) {
    throw new Error("Database is not properly configured. Please check your DATABASE_URL environment variable.")
  }

  try {
    // Build SQL query
    let sqlQuery = `
      SELECT 
        li.id,
        li.title,
        COALESCE(u.full_name, li.author) as author,
        li.type,
        COUNT(c.id) as chapters,
        li.status,
        li.category,
        li.updated_at as "lastUpdated",
        COALESCE(li.cover_image_url, li.cover_image, '/placeholder.svg?height=400&width=300') as cover,
        li.description,
        COALESCE(li.tags, ARRAY[]::text[]) as tags
      FROM 
        library_items li
      LEFT JOIN 
        users u ON li.author_id = u.id
      LEFT JOIN 
        chapters c ON li.id = c.library_item_id
    `

    const whereConditions = []
    const params = []
    let paramIndex = 1

    // Add search filter
    if (searchTerm) {
      whereConditions.push(`(li.title ILIKE $${paramIndex} OR COALESCE(u.full_name, li.author) ILIKE $${paramIndex})`)
      params.push(`%${searchTerm}%`)
      paramIndex++
    }

    // Add type filter
    if (type !== "all") {
      whereConditions.push(`li.type = $${paramIndex}`)
      params.push(type)
      paramIndex++
    }

    // Add category filter
    if (category !== "all") {
      whereConditions.push(`li.category = $${paramIndex}`)
      params.push(category)
      paramIndex++
    }

    // Add status filter
    if (status !== "all") {
      whereConditions.push(`li.status = $${paramIndex}`)
      params.push(status)
      paramIndex++
    }

    // Add WHERE clause if conditions exist
    if (whereConditions.length > 0) {
      sqlQuery += ` WHERE ${whereConditions.join(" AND ")}`
    }

    // Add GROUP BY and ORDER BY
    sqlQuery += `
      GROUP BY li.id, li.title, u.full_name, li.author, li.type, li.status, li.category, li.updated_at, li.cover_image_url, li.cover_image, li.description, li.tags
      ORDER BY ${sortBy === "lastUpdated" ? "li.updated_at" : sortBy === "author" ? "COALESCE(u.full_name, li.author)" : `li.${sortBy}`} ${sortOrder === "desc" ? "DESC" : "ASC"}
    `

    // Use Neon client directly with tagged template literals
    const sqlClient = neon(process.env.DATABASE_URL!)

    // For now, let's use a simple query to get all books and filter in JavaScript
    // This is a temporary solution until we figure out dynamic queries with Neon
    const result = await sqlClient`
      SELECT
        li.id,
        li.title,
        COALESCE(u.full_name, li.author) as author,
        li.type,
        COUNT(c.id) as chapters,
        li.status,
        li.category,
        li.updated_at as "lastUpdated",
        COALESCE(li.cover_image_url, li.cover_image, '/placeholder.svg?height=400&width=300') as cover,
        li.description,
        COALESCE(li.tags, ARRAY[]::text[]) as tags
      FROM
        library_items li
      LEFT JOIN
        users u ON li.author_id = u.id
      LEFT JOIN
        chapters c ON li.id = c.library_item_id
      GROUP BY li.id, li.title, u.full_name, li.author, li.type, li.status, li.category, li.updated_at, li.cover_image_url, li.cover_image, li.description, li.tags
      ORDER BY li.title ASC
    `

    if (!result || !Array.isArray(result)) {
      return []
    }

    const books = result.map((book: any) => ({
      id: book.id,
      title: book.title || "Untitled",
      author: book.author || "Unknown Author",
      type: book.type || "Book",
      chapters: Number.parseInt(book.chapters) || 0,
      status: book.status || "Draft",
      category: book.category || "Uncategorized",
      lastUpdated: book.lastUpdated ? new Date(book.lastUpdated).toISOString().split("T")[0] : "",
      tags: Array.isArray(book.tags) ? book.tags : [],
      cover: book.cover || "/placeholder.svg?height=400&width=300",
      description: book.description || "",
    }))

    return books
  } catch (error) {
    console.error("Error fetching books:", error)
    throw new Error(`Failed to fetch books: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
