import { neon } from "@neondatabase/serverless"
import { isDatabaseConfigured } from "@/lib/database"

export interface Chapter {
  id: string
  title: string
  bookTitle: string
  bookId: string
  author: string
  status: string
  wordCount: number
  lastUpdated: string
  subjects: string[]
}

export async function getChapters(searchTerm = "", bookId = "all", status = "all"): Promise<Chapter[]> {
  if (!isDatabaseConfigured()) {
    throw new Error("Database is not properly configured. Please check your DATABASE_URL environment variable.")
  }

  try {
    // Use direct query construction to avoid parameter issues
    let sql = `
      SELECT
        c.id,
        c.title,
        li.title as "bookTitle",
        li.id as "bookId",
        COALESCE(u.full_name, li.author) as author,
        c.status,
        c.word_count as "wordCount",
        c.updated_at as "lastUpdated",
        COALESCE(
          ARRAY_AGG(DISTINCT s.name) FILTER (WHERE s.name IS NOT NULL),
          ARRAY[]::text[]
        ) as subjects
      FROM
        chapters c
      LEFT JOIN
        library_items li ON c.library_item_id = li.id
      LEFT JOIN
        users u ON li.author_id = u.id
      LEFT JOIN
        library_item_subjects lis ON li.id = lis.library_item_id
      LEFT JOIN
        subjects s ON lis.subject_id = s.id
      WHERE 1=1
    `

    if (searchTerm) {
      const escapedSearchTerm = searchTerm.replace(/'/g, "''")
      sql += ` AND (c.title ILIKE '%${escapedSearchTerm}%' OR COALESCE(u.full_name, li.author) ILIKE '%${escapedSearchTerm}%' OR EXISTS (
        SELECT 1 FROM library_item_subjects lis2
        JOIN subjects s2 ON lis2.subject_id = s2.id
        WHERE lis2.library_item_id = li.id AND s2.name ILIKE '%${escapedSearchTerm}%'
      ))`
    }

    if (bookId !== "all") {
      const numericBookId = Number.parseInt(bookId)
      if (!isNaN(numericBookId)) {
        sql += ` AND li.id = ${numericBookId}`
      }
    }

    if (status !== "all") {
      const escapedStatus = status.replace(/'/g, "''")
      sql += ` AND c.status = '${escapedStatus}'`
    }

    sql += ` GROUP BY c.id, li.title, li.id, u.full_name, li.author ORDER BY c.updated_at DESC`

    // Use Neon client directly to avoid issues with the query wrapper
    const sqlClient = neon(process.env.DATABASE_URL!)
    const result = await sqlClient.unsafe(sql)

    if (!result || !Array.isArray(result)) {
      return []
    }

    // Debug: log the first row to see the structure
    if (result.length > 0) {
      console.log("Sample chapter data:", result[0])
    }

    const chapters = result.map((chapter: any) => ({
      id: chapter.id?.toString() || "",
      title: chapter.title || "Untitled Chapter",
      bookTitle: chapter.bookTitle || "Unknown Book",
      bookId: chapter.bookId?.toString() || "",
      author: chapter.author || "Unknown Author",
      status: chapter.status || "Draft",
      wordCount: Number.parseInt(chapter.wordCount) || 0,
      lastUpdated: chapter.lastUpdated ? new Date(chapter.lastUpdated).toISOString().split("T")[0] : "",
      subjects: Array.isArray(chapter.subjects) ? chapter.subjects.filter(Boolean) : [],
    }))

    return chapters
  } catch (error) {
    console.error("Error fetching chapters:", error)
    throw new Error(`Failed to fetch chapters: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}
