import { neon } from "@neondatabase/serverless"

export function isDatabaseConfigured(): boolean {
  return !!process.env.DATABASE_URL
}

export function getConnection() {
  if (!isDatabaseConfigured()) {
    throw new Error("Database not configured. Please set DATABASE_URL environment variable.")
  }

  return neon(process.env.DATABASE_URL!)
}

export async function testConnection(): Promise<boolean> {
  try {
    const sql = getConnection()
    await sql`SELECT 1`
    return true
  } catch (error) {
    console.error("Database connection test failed:", error)
    return false
  }
}

// Explicit named exports
export { isDatabaseConfigured as default }
