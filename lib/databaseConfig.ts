import { neon } from "@neondatabase/serverless"

export function isDatabaseConfigured(): boolean {
  return !!process.env.DATABASE_URL
}

export function getDatabaseUrl(): string | null {
  return process.env.DATABASE_URL || null
}

export async function validateDatabaseConnection(): Promise<boolean> {
  try {
    if (!isDatabaseConfigured()) {
      return false
    }

    const sql = neon(process.env.DATABASE_URL!)
    await sql`SELECT 1`
    return true
  } catch (error) {
    console.error("Database connection validation failed:", error)
    return false
  }
}

// Explicit named exports
export { isDatabaseConfigured as default }
