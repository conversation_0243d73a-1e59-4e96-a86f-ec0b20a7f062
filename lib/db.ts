import { neon } from "@neondatabase/serverless"

// Determine if we're in a browser environment
const isBrowser = typeof window !== "undefined"

let sqlClient: any = null

// Initialize the Neon client only on the server side
if (!isBrowser && process.env.DATABASE_URL) {
  sqlClient = neon(process.env.DATABASE_URL)
}

export async function query(text: string, params?: any[]) {
  try {
    if (isBrowser) {
      // In browser environment, throw an error - all database operations should happen server-side
      throw new Error("Database operations are not allowed in the browser. Use server actions instead.")
    }

    if (!process.env.DATABASE_URL) {
      throw new Error("Database connection not initialized. Please check your DATABASE_URL environment variable.")
    }

    // Create a fresh Neon client for each query to avoid issues
    const { neon } = await import("@neondatabase/serverless")
    const sql = neon(process.env.DATABASE_URL)

    // Execute the query using Neon
    let result
    if (params && params.length > 0) {
      // Use unsafe method with parameters for parameterized queries
      result = await sql.unsafe(text, params)
    } else {
      // Use unsafe method without parameters for simple queries
      result = await sql.unsafe(text)
    }

    // Neon returns results directly as an array, so we wrap it in rows for compatibility
    return { rows: Array.isArray(result) ? result : [result] }
  } catch (error) {
    console.error("Database query error:", error)
    console.error("Query:", text)
    console.error("Params:", params)
    throw new Error(`Database query failed: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

// Helper function for transactions
export async function transaction(queries: Array<{ text: string; params?: any[] }>) {
  try {
    if (isBrowser) {
      throw new Error("Database transactions are not allowed in the browser.")
    }

    if (!sqlClient) {
      throw new Error("Database connection not initialized.")
    }

    // Note: Neon serverless doesn't support traditional transactions
    // For now, we'll execute queries sequentially
    const results = []
    for (const queryObj of queries) {
      let result
      if (queryObj.params && queryObj.params.length > 0) {
        result = await sqlClient.unsafe(queryObj.text, queryObj.params)
      } else {
        result = await sqlClient.unsafe(queryObj.text)
      }
      results.push({ rows: Array.isArray(result) ? result : [result] })
    }
    return results
  } catch (error) {
    console.error("Transaction error:", error)
    throw new Error(`Transaction failed: ${error instanceof Error ? error.message : "Unknown error"}`)
  }
}

// Database connection test
export async function testConnection() {
  try {
    if (isBrowser) {
      throw new Error("Connection test not available in browser.")
    }

    if (!sqlClient) {
      throw new Error("Database connection not initialized.")
    }

    const result = await sqlClient`SELECT 1 as test, NOW() as timestamp`
    return {
      success: true,
      result: result[0],
      message: "Database connection successful",
    }
  } catch (error) {
    console.error("Database connection test failed:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      message: "Database connection failed",
    }
  }
}

// Helper to check if database is properly configured
export function isDatabaseConfigured(): boolean {
  return !isBrowser && !!process.env.DATABASE_URL && !!sqlClient
}

// This application uses PostgreSQL via Neon exclusively

export async function getLibraries() {
  try {
    const result = await query("SELECT * FROM library_items ORDER BY title")
    return result.rows
  } catch (error) {
    console.error("Error fetching libraries:", error)
    return []
  }
}
