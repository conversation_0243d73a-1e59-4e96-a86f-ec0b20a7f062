-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS activity_logs CASCADE;
DROP TABLE IF EXISTS drafts CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS workspace_collaborators CASCADE;
DROP TABLE IF EXISTS workspace_subjects CASCADE;
DROP TABLE IF EXISTS workspaces CASCADE;
DROP TABLE IF EXISTS study_guide_subjects CASCADE;
DROP TABLE IF EXISTS study_guides CASCADE;
DROP TABLE IF EXISTS chapter_scriptures CASCADE;
DROP TABLE IF EXISTS scripture_references CASCADE;
DROP TABLE IF EXISTS video_subjects CASCADE;
DROP TABLE IF EXISTS transcripts CASCADE;
DROP TABLE IF EXISTS videos CASCADE;
DROP TABLE IF EXISTS chapter_subjects CASCADE;
DROP TABLE IF EXISTS book_subjects CASCADE;
DROP TABLE IF EXISTS subjects CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS books CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Users table for authentication and user management
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'user', -- 'admin', 'editor', 'user'
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Books table for storing book metadata
CREATE TABLE books (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  author_id INTEGER REFERENCES users(id),
  type VARCHAR(50) NOT NULL, -- 'Book', 'Study Guide', 'Course', 'Transcript'
  status VARCHAR(20) NOT NULL DEFAULT 'Draft', -- 'Published', 'Draft', 'Review'
  category VARCHAR(100),
  description TEXT,
  cover_image VARCHAR(255),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP
);

-- Chapters table for individual chapters within books
CREATE TABLE chapters (
  id SERIAL PRIMARY KEY,
  book_id INTEGER REFERENCES books(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  order_index INTEGER NOT NULL,
  word_count INTEGER DEFAULT 0,
  status VARCHAR(20) NOT NULL DEFAULT 'Draft', -- 'Published', 'Draft', 'Review'
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Videos table for sermon/teaching videos
CREATE TABLE videos (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  minister_id INTEGER REFERENCES users(id),
  youtube_id VARCHAR(50),
  duration VARCHAR(10), -- stored as 'HH:MM:SS'
  description TEXT,
  thumbnail_url VARCHAR(255),
  views INTEGER DEFAULT 0,
  published_date DATE,
  transcription_status VARCHAR(20) DEFAULT 'pending', -- 'completed', 'in-progress', 'pending'
  transcription_progress INTEGER DEFAULT 0, -- percentage complete
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Transcripts table linked to videos
CREATE TABLE transcripts (
  id SERIAL PRIMARY KEY,
  video_id INTEGER REFERENCES videos(id) ON DELETE CASCADE,
  content TEXT,
  is_edited BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Subjects/Tags table for categorization
CREATE TABLE subjects (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Junction table for book-subject relationship (many-to-many)
CREATE TABLE book_subjects (
  book_id INTEGER REFERENCES books(id) ON DELETE CASCADE,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  PRIMARY KEY (book_id, subject_id)
);

-- Junction table for chapter-subject relationship (many-to-many)
CREATE TABLE chapter_subjects (
  chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  PRIMARY KEY (chapter_id, subject_id)
);

-- Junction table for video-subject relationship (many-to-many)
CREATE TABLE video_subjects (
  video_id INTEGER REFERENCES videos(id) ON DELETE CASCADE,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  PRIMARY KEY (video_id, subject_id)
);

-- Scripture references table
CREATE TABLE scripture_references (
  id SERIAL PRIMARY KEY,
  book VARCHAR(50) NOT NULL,
  chapter INTEGER NOT NULL,
  verse_start INTEGER NOT NULL,
  verse_end INTEGER,
  reference_text TEXT
);

-- Junction table for chapter-scripture relationship (many-to-many)
CREATE TABLE chapter_scriptures (
  chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
  scripture_id INTEGER REFERENCES scripture_references(id) ON DELETE CASCADE,
  PRIMARY KEY (chapter_id, scripture_id)
);

-- Study guides table
CREATE TABLE study_guides (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  author_id INTEGER REFERENCES users(id),
  target_audience VARCHAR(100),
  difficulty VARCHAR(50), -- 'Beginner', 'Intermediate', 'Advanced'
  estimated_time VARCHAR(50), -- e.g., '6 weeks'
  in_faith_library BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) NOT NULL DEFAULT 'Draft', -- 'Published', 'Draft', 'Review'
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Junction table for study guide subjects
CREATE TABLE study_guide_subjects (
  study_guide_id INTEGER REFERENCES study_guides(id) ON DELETE CASCADE,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  PRIMARY KEY (study_guide_id, subject_id)
);

-- Workspaces table for collaborative content creation
CREATE TABLE workspaces (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  type VARCHAR(50), -- 'Devotional', 'Study Guide', 'Sermon Series', etc.
  status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'active', 'draft', 'review'
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Junction table for workspace-subject relationship
CREATE TABLE workspace_subjects (
  workspace_id INTEGER REFERENCES workspaces(id) ON DELETE CASCADE,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  PRIMARY KEY (workspace_id, subject_id)
);

-- Junction table for workspace collaborators
CREATE TABLE workspace_collaborators (
  workspace_id INTEGER REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(50) DEFAULT 'editor', -- 'owner', 'editor', 'viewer'
  PRIMARY KEY (workspace_id, user_id)
);

-- Tasks table for user task management
CREATE TABLE tasks (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  assigned_to INTEGER REFERENCES users(id),
  assigned_by INTEGER REFERENCES users(id),
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'in-progress', 'completed'
  priority VARCHAR(20) DEFAULT 'medium', -- 'high', 'medium', 'low'
  due_date DATE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Activity log for tracking user actions
CREATE TABLE activity_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  action_type VARCHAR(50) NOT NULL, -- 'created', 'updated', 'published', 'transcribed', etc.
  entity_type VARCHAR(50) NOT NULL, -- 'book', 'chapter', 'video', etc.
  entity_id INTEGER NOT NULL,
  details TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Drafts table for work in progress
CREATE TABLE drafts (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  type VARCHAR(50) NOT NULL, -- 'Chapter', 'Book', 'Notes', etc.
  user_id INTEGER REFERENCES users(id),
  progress INTEGER DEFAULT 0, -- percentage complete
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_books_author_id ON books(author_id);
CREATE INDEX idx_books_status ON books(status);
CREATE INDEX idx_chapters_book_id ON chapters(book_id);
CREATE INDEX idx_videos_minister_id ON videos(minister_id);
CREATE INDEX idx_videos_published_date ON videos(published_date);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);
