#!/usr/bin/env node

/**
 * Database Setup Script
 * This script sets up the database schema and seed data for the Chapters application
 */

const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');

async function setupDatabase() {
  console.log('🚀 Starting database setup...\n');

  // Check if DATABASE_URL is configured
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set.');
    console.log('Please add your Neon database URL to .env.local file:');
    console.log('DATABASE_URL="your_neon_database_url_here"');
    process.exit(1);
  }

  try {
    // Initialize database connection
    const sql = neon(process.env.DATABASE_URL);
    console.log('✅ Connected to database');

    // Test connection
    await sql`SELECT 1`;
    console.log('✅ Database connection test passed');

    // Read and execute schema
    console.log('\n📋 Creating database schema...');
    const schemaPath = path.join(__dirname, 'unified-schema.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // Split schema into individual statements and execute
    const schemaStatements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of schemaStatements) {
      if (statement.trim()) {
        await sql.unsafe(statement);
      }
    }
    console.log('✅ Database schema created successfully');

    // Read and execute seed data
    console.log('\n🌱 Inserting seed data...');
    const seedPath = path.join(__dirname, 'unified-seed-data.sql');
    const seedSQL = fs.readFileSync(seedPath, 'utf8');
    
    // Split seed data into individual statements and execute
    const seedStatements = seedSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of seedStatements) {
      if (statement.trim()) {
        await sql.unsafe(statement);
      }
    }
    console.log('✅ Seed data inserted successfully');

    // Verify setup by checking table counts
    console.log('\n📊 Verifying database setup...');
    const userCount = await sql`SELECT COUNT(*) as count FROM users`;
    const subjectCount = await sql`SELECT COUNT(*) as count FROM subjects`;
    const libraryItemCount = await sql`SELECT COUNT(*) as count FROM library_items`;
    const chapterCount = await sql`SELECT COUNT(*) as count FROM chapters`;

    console.log(`✅ Users: ${userCount[0].count}`);
    console.log(`✅ Subjects: ${subjectCount[0].count}`);
    console.log(`✅ Library Items: ${libraryItemCount[0].count}`);
    console.log(`✅ Chapters: ${chapterCount[0].count}`);

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\nYour database is now ready to use with the Chapters application.');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.error('\nFull error:', error);
    process.exit(1);
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Run setup
setupDatabase();
