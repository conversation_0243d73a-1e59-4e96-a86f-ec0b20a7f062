#!/usr/bin/env node

/**
 * Database Connection Test Script
 * This script tests the database connection and displays basic information
 */

const { neon } = require('@neondatabase/serverless');

async function testDatabase() {
  console.log('🔍 Testing database connection...\n');

  // Check if DATABASE_URL is configured
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set.');
    console.log('Please add your Neon database URL to .env.local file:');
    console.log('DATABASE_URL="your_neon_database_url_here"');
    return;
  }

  try {
    // Initialize database connection
    const sql = neon(process.env.DATABASE_URL);
    
    // Test basic connection
    const result = await sql`SELECT 1 as test`;
    console.log('✅ Database connection successful');

    // Check if tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;

    if (tables.length === 0) {
      console.log('⚠️  No tables found. Run the setup script first:');
      console.log('   node scripts/setup-database.js');
    } else {
      console.log('\n📋 Found tables:');
      tables.forEach(table => {
        console.log(`   - ${table.table_name}`);
      });

      // Get record counts
      console.log('\n📊 Record counts:');
      try {
        const userCount = await sql`SELECT COUNT(*) as count FROM users`;
        console.log(`   Users: ${userCount[0].count}`);
      } catch (e) {
        console.log('   Users: Table not found');
      }

      try {
        const subjectCount = await sql`SELECT COUNT(*) as count FROM subjects`;
        console.log(`   Subjects: ${subjectCount[0].count}`);
      } catch (e) {
        console.log('   Subjects: Table not found');
      }

      try {
        const libraryItemCount = await sql`SELECT COUNT(*) as count FROM library_items`;
        console.log(`   Library Items: ${libraryItemCount[0].count}`);
      } catch (e) {
        console.log('   Library Items: Table not found');
      }

      try {
        const chapterCount = await sql`SELECT COUNT(*) as count FROM chapters`;
        console.log(`   Chapters: ${chapterCount[0].count}`);
      } catch (e) {
        console.log('   Chapters: Table not found');
      }
    }

    console.log('\n✅ Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('\nFull error:', error);
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Run test
testDatabase();
