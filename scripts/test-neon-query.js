#!/usr/bin/env node

/**
 * Test Neon Query Methods
 * This script tests different ways to execute queries with Neon
 */

const { neon } = require('@neondatabase/serverless');

async function testNeonQueries() {
  console.log('🔍 Testing Neon query methods...\n');

  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set.');
    return;
  }

  try {
    const sql = neon(process.env.DATABASE_URL);
    
    console.log('1. Testing simple tagged template query:');
    const result1 = await sql`SELECT COUNT(*) as count FROM chapters`;
    console.log('Result:', result1);
    console.log('Type:', typeof result1);
    console.log('Is Array:', Array.isArray(result1));
    
    console.log('\n2. Testing unsafe method:');
    const result2 = await sql.unsafe('SELECT COUNT(*) as count FROM chapters');
    console.log('Result:', result2);
    console.log('Type:', typeof result2);
    console.log('Is Array:', Array.isArray(result2));
    
    console.log('\n3. Testing unsafe with parameters:');
    const result3 = await sql.unsafe('SELECT * FROM chapters WHERE status = $1 LIMIT 1', ['Published']);
    console.log('Result:', result3);
    console.log('Type:', typeof result3);
    console.log('Is Array:', Array.isArray(result3));
    
    console.log('\n4. Testing complex query:');
    const result4 = await sql.unsafe(`
      SELECT 
        c.id,
        c.title,
        li.title as book_title,
        c.status
      FROM 
        chapters c
      LEFT JOIN 
        library_items li ON c.library_item_id = li.id
      LIMIT 2
    `);
    console.log('Result:', result4);
    console.log('Type:', typeof result4);
    console.log('Is Array:', Array.isArray(result4));
    if (Array.isArray(result4) && result4.length > 0) {
      console.log('First item:', result4[0]);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Run test
testNeonQueries();
