-- Drop existing tables if they exist
DROP TABLE IF EXISTS book_subjects CASCADE;
DROP TABLE IF EXISTS library_item_subjects CASCADE;
DROP TABLE IF EXISTS books CASCADE;
DROP TABLE IF EXISTS study_guides CASCADE;
DROP TABLE IF EXISTS transcripts CASCADE;
DROP TABLE IF EXISTS library_items CASCADE;
DROP TABLE IF EXISTS chapters CASCADE;
DROP TABLE IF EXISTS subjects CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS videos CASCADE;

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create subjects table
CREATE TABLE subjects (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create unified library_items table (replaces books, study_guides, transcripts)
CREATE TABLE library_items (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    author VARCHAR(255) NOT NULL,
    author_id INTEGER REFERENCES users(id),
    type VARCHAR(50) NOT NULL CHECK (type IN ('Book', 'Study Guide', 'Transcript', 'Course')),
    status VARCHAR(50) DEFAULT 'Draft' CHECK (status IN ('Draft', 'Published', 'Archived')),
    category VARCHAR(255),
    description TEXT,
    cover_image VARCHAR(500),
    
    -- Common metadata
    tags TEXT[], -- Array of tag strings
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Transcript-specific fields
    minister VARCHAR(255), -- For transcripts
    duration VARCHAR(20), -- For transcripts (e.g., "45:30")
    published_date DATE, -- For transcripts
    youtube_id VARCHAR(100), -- For video transcripts
    
    -- Study Guide specific fields
    target_audience VARCHAR(255), -- For study guides
    difficulty VARCHAR(50), -- Beginner, Intermediate, Advanced
    estimated_time VARCHAR(100), -- e.g., "4 weeks", "2 hours"
    in_faith_library BOOLEAN DEFAULT FALSE,
    
    -- Additional metadata
    in_app BOOLEAN DEFAULT FALSE, -- Whether item is available in mobile app
    event VARCHAR(255), -- Event where content was taught (e.g., "Founders Week", "School of the Prophets")
    
    -- Purchase and access links
    paperback_link VARCHAR(500), -- Link to purchase paperback version
    ebook_link VARCHAR(500), -- Link to purchase e-book version
    faith_library_link VARCHAR(500), -- Link to Faith Library version
    amazon_link VARCHAR(500), -- Link to Amazon listing
    cover_image_url VARCHAR(500), -- URL to high-resolution cover image
    
    -- Course specific fields
    course_level VARCHAR(50), -- For courses
    prerequisites TEXT -- For courses
);

-- Create chapters table (linked to library_items)
CREATE TABLE chapters (
    id SERIAL PRIMARY KEY,
    library_item_id INTEGER REFERENCES library_items(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    order_index INTEGER NOT NULL,
    word_count INTEGER DEFAULT 0,
    status VARCHAR(50) DEFAULT 'Draft' CHECK (status IN ('Draft', 'Published', 'Archived')),
    scripture_references TEXT[],
    subjects TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create library_item_subjects junction table
CREATE TABLE library_item_subjects (
    library_item_id INTEGER REFERENCES library_items(id) ON DELETE CASCADE,
    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
    PRIMARY KEY (library_item_id, subject_id)
);

-- Create videos table (for video content management)
CREATE TABLE videos (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    duration VARCHAR(20),
    published_date DATE,
    transcript_id INTEGER REFERENCES library_items(id), -- Link to transcript
    minister VARCHAR(255),
    youtube_id VARCHAR(100),
    subjects TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_library_items_type ON library_items(type);
CREATE INDEX idx_library_items_status ON library_items(status);
CREATE INDEX idx_library_items_category ON library_items(category);
CREATE INDEX idx_library_items_author ON library_items(author);
CREATE INDEX idx_library_items_created_at ON library_items(created_at);
CREATE INDEX idx_library_items_updated_at ON library_items(updated_at);

CREATE INDEX idx_chapters_library_item_id ON chapters(library_item_id);
CREATE INDEX idx_chapters_order_index ON chapters(order_index);
CREATE INDEX idx_chapters_status ON chapters(status);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);

CREATE INDEX idx_subjects_name ON subjects(name);

-- Create triggers to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_library_items_updated_at BEFORE UPDATE ON library_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_chapters_updated_at BEFORE UPDATE ON chapters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subjects_updated_at BEFORE UPDATE ON subjects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
